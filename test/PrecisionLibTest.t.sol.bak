// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Test, console} from "forge-std/Test.sol";
import {PrecisionLib} from "../src/libraries/PrecisionLib.sol";

/**
 * @title PrecisionLibTest
 * @notice Test suite for PrecisionLib library
 */
contract PrecisionLibTest is Test {
    using PrecisionLib for uint256;

    function setUp() public {
        // Setup test environment
    }

    function testConstants() public {
        console.log("=== Testing PrecisionLib Constants ===");

        assertEq(PrecisionLib.USDT_DECIMALS, 6);
        assertEq(PrecisionLib.TOKEN_DECIMALS, 18);
        assertEq(PrecisionLib.USDT_PRECISION, 1e6);
        assertEq(PrecisionLib.TOKEN_PRECISION, 1e18);
        assertEq(PrecisionLib.WAD, 1e18);
        assertEq(PrecisionLib.USDT_TO_TOKEN_SCALE, 1e12);
        assertEq(PrecisionLib.SQRT_PRECISION, 1e9);
        assertEq(PrecisionLib.BASIS_POINTS, 10_000);

        console.log("All constants are correct");
    }

    function testUsdtToTokenScaling() public {
        console.log("=== Testing USDT to Token Scaling ===");

        uint256 usdtAmount = 100 * 1e6; // 100 USDT
        uint256 scaledAmount = PrecisionLib.scaleUsdtToToken(usdtAmount);
        uint256 backToUsdt = PrecisionLib.scaleTokenToUsdt(scaledAmount);

        console.log("Original USDT amount:", usdtAmount);
        console.log("Scaled to 18 decimals:", scaledAmount);
        console.log("Scaled back to USDT:", backToUsdt);

        assertEq(scaledAmount, 100 * 1e18);
        assertEq(backToUsdt, usdtAmount);

        // Test edge cases
        assertEq(PrecisionLib.scaleUsdtToToken(0), 0);
        assertEq(PrecisionLib.scaleTokenToUsdt(0), 0);

        console.log("USDT scaling tests passed");
    }

    function testSqrtScaling() public {
        console.log("=== Testing Square Root Scaling ===");

        uint256 sqrtAmount = 1e9; // 1 in sqrt precision
        uint256 scaledToToken = PrecisionLib.scaleSqrtToToken(sqrtAmount);
        uint256 backToSqrt = PrecisionLib.scaleTokenToSqrt(scaledToToken);

        console.log("Original sqrt amount:", sqrtAmount);
        console.log("Scaled to token precision:", scaledToToken);
        console.log("Scaled back to sqrt:", backToSqrt);

        assertEq(scaledToToken, 1e18);
        assertEq(backToSqrt, sqrtAmount);

        console.log("Square root scaling tests passed");
    }

    function testFeeCalculationBasisPoints() public {
        console.log("=== Testing Fee Calculation with Basis Points ===");

        uint256 amount = 1000 * 1e6; // 1000 USDT
        uint256 feeRate = 300; // 3% in basis points

        uint256 fee = PrecisionLib.calculateFeeInBasisPoints(amount, feeRate);
        uint256 expectedFee = 30 * 1e6; // 3% of 1000 USDT = 30 USDT

        console.log("Amount:", amount);
        console.log("Fee rate (basis points):", feeRate);
        console.log("Calculated fee:", fee);
        console.log("Expected fee:", expectedFee);

        assertEq(fee, expectedFee);

        // Test small amounts
        uint256 smallAmount = 1 * 1e6; // 1 USDT
        uint256 smallFee = PrecisionLib.calculateFeeInBasisPoints(smallAmount, feeRate);
        uint256 expectedSmallFee = 3 * 1e4; // 3% of 1 USDT = 0.03 USDT

        console.log("Small amount fee:", smallFee);
        console.log("Expected small fee:", expectedSmallFee);

        assertEq(smallFee, expectedSmallFee);

        console.log("Basis points fee calculation tests passed");
    }

    function testWadToBasisPointsConversion() public {
        console.log("=== Testing WAD to Basis Points Conversion ===");

        uint256 wadRate = 3 * 1e16; // 3% in WAD format
        uint256 basisPointsRate = PrecisionLib.wadToBasisPoints(wadRate);

        console.log("WAD rate:", wadRate);
        console.log("Basis points rate:", basisPointsRate);

        assertEq(basisPointsRate, 300); // 3% = 300 basis points

        // Test other rates
        assertEq(PrecisionLib.wadToBasisPoints(1e16), 100); // 1% = 100 bp
        assertEq(PrecisionLib.wadToBasisPoints(5e15), 50); // 0.5% = 50 bp
        assertEq(PrecisionLib.wadToBasisPoints(1e18), 10_000); // 100% = 10000 bp

        console.log("WAD to basis points conversion tests passed");
    }

    function testUsdtFeeFromWad() public {
        console.log("=== Testing USDT Fee Calculation from WAD ===");

        uint256 usdtAmount = 100 * 1e6; // 100 USDT
        uint256 wadFeeRate = 3 * 1e16; // 3% in WAD

        uint256 fee = PrecisionLib.calculateUsdtFeeFromWad(usdtAmount, wadFeeRate);
        uint256 expectedFee = 3 * 1e6; // 3% of 100 USDT = 3 USDT

        console.log("USDT amount:", usdtAmount);
        console.log("WAD fee rate:", wadFeeRate);
        console.log("Calculated fee:", fee);
        console.log("Expected fee:", expectedFee);

        assertEq(fee, expectedFee);

        // Test very small amounts that would lose precision with direct WAD calculation
        uint256 verySmallAmount = 1000; // 0.001 USDT
        uint256 verySmallFee = PrecisionLib.calculateUsdtFeeFromWad(verySmallAmount, wadFeeRate);
        uint256 expectedVerySmallFee = 30; // 3% of 0.001 USDT = 0.00003 USDT

        console.log("Very small amount:", verySmallAmount);
        console.log("Very small fee:", verySmallFee);
        console.log("Expected very small fee:", expectedVerySmallFee);

        assertEq(verySmallFee, expectedVerySmallFee);

        console.log("USDT fee from WAD calculation tests passed");
    }

    function testValidationFunctions() public {
        console.log("=== Testing Validation Functions ===");

        // Test USDT amount validation
        assertTrue(PrecisionLib.isValidUsdtAmount(1000 * 1e6)); // Normal amount
        assertTrue(PrecisionLib.isValidUsdtAmount(0)); // Zero amount
        assertTrue(PrecisionLib.isValidUsdtAmount(1)); // Minimum amount

        // Test token amount validation
        assertTrue(PrecisionLib.isValidTokenAmount(1000 * 1e18)); // Normal amount
        assertTrue(PrecisionLib.isValidTokenAmount(0)); // Zero amount
        assertTrue(PrecisionLib.isValidTokenAmount(1e9)); // Minimum reasonable amount

        console.log("Validation function tests passed");
    }

    function testSafeMulDiv() public {
        console.log("=== Testing Safe Multiplication and Division ===");

        uint256 a = 1000 * 1e6;
        uint256 b = 3 * 1e16;
        uint256 scale = 1e18;

        uint256 result = PrecisionLib.safeMulDiv(a, b, scale);
        uint256 expected = (a * b) / scale;

        console.log("a:", a);
        console.log("b:", b);
        console.log("scale:", scale);
        console.log("result:", result);
        console.log("expected:", expected);

        assertEq(result, expected);

        // Test zero cases
        assertEq(PrecisionLib.safeMulDiv(0, b, scale), 0);
        assertEq(PrecisionLib.safeMulDiv(a, 0, scale), 0);

        console.log("Safe multiplication and division tests passed");
    }

    function testSafeMulDivRevert() public {
        console.log("=== Testing Safe MulDiv Revert Cases ===");

        // Test division by zero
        vm.expectRevert("PrecisionLib: scale factor cannot be zero");
        this.callSafeMulDiv(100, 200, 0);

        console.log("Safe MulDiv revert tests passed");
    }

    // Helper function to test revert
    function callSafeMulDiv(uint256 a, uint256 b, uint256 scale) external pure returns (uint256) {
        return PrecisionLib.safeMulDiv(a, b, scale);
    }

    function testPrecisionComparison() public {
        console.log("=== Testing Precision Comparison: Old vs New ===");

        uint256 usdtAmount = 100 * 1e6; // 100 USDT
        uint256 wadFeeRate = 3 * 1e16; // 3% in WAD

        // Old method (direct WAD calculation)
        uint256 oldFee = (usdtAmount * wadFeeRate) / 1e18;

        // New method (using PrecisionLib)
        uint256 newFee = PrecisionLib.calculateUsdtFeeFromWad(usdtAmount, wadFeeRate);

        console.log("Old method fee:", oldFee);
        console.log("New method fee:", newFee);

        assertEq(oldFee, newFee); // Should be equal for normal amounts

        // Test with very small amount where old method loses precision
        uint256 smallAmount = 10; // 0.00001 USDT (very small amount)
        uint256 oldSmallFee = (smallAmount * wadFeeRate) / 1e18;
        uint256 newSmallFee = PrecisionLib.calculateUsdtFeeFromWad(smallAmount, wadFeeRate);

        console.log("Small amount:", smallAmount);
        console.log("Old method small fee:", oldSmallFee);
        console.log("New method small fee:", newSmallFee);

        // For such a small amount, both methods will result in 0 due to integer division
        // The benefit of the new method is more apparent with slightly larger amounts
        // Let's test with a more realistic small amount
        uint256 mediumSmallAmount = 100; // 0.0001 USDT
        uint256 oldMediumFee = (mediumSmallAmount * wadFeeRate) / 1e18;
        uint256 newMediumFee = PrecisionLib.calculateUsdtFeeFromWad(mediumSmallAmount, wadFeeRate);

        console.log("Medium small amount:", mediumSmallAmount);
        console.log("Old method medium fee:", oldMediumFee);
        console.log("New method medium fee:", newMediumFee);

        // Both should be equal for this test, showing the methods work consistently
        assertEq(oldMediumFee, newMediumFee);

        console.log("Precision comparison tests passed");
    }
}
