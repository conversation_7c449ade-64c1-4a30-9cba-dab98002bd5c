// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import {Test, console} from "forge-std/Test.sol";
import {PrecisionLib} from "../src/libraries/PrecisionLib.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";

// Mock ERC20 token with configurable decimals
contract MockToken is IERC20Metadata {
    string private _name;
    string private _symbol;
    uint8 private _decimals;
    uint256 private _totalSupply;
    mapping(address => uint256) private _balances;
    mapping(address => mapping(address => uint256)) private _allowances;

    constructor(string memory name_, string memory symbol_, uint8 decimals_) {
        _name = name_;
        _symbol = symbol_;
        _decimals = decimals_;
    }

    function name() public view override returns (string memory) {
        return _name;
    }

    function symbol() public view override returns (string memory) {
        return _symbol;
    }

    function decimals() public view override returns (uint8) {
        return _decimals;
    }

    function totalSupply() public view override returns (uint256) {
        return _totalSupply;
    }

    function balanceOf(address account) public view override returns (uint256) {
        return _balances[account];
    }

    function transfer(address to, uint256 amount) public override returns (bool) {
        _transfer(msg.sender, to, amount);
        return true;
    }

    function allowance(address owner, address spender) public view override returns (uint256) {
        return _allowances[owner][spender];
    }

    function approve(address spender, uint256 amount) public override returns (bool) {
        _approve(msg.sender, spender, amount);
        return true;
    }

    function transferFrom(address from, address to, uint256 amount) public override returns (bool) {
        uint256 currentAllowance = _allowances[from][msg.sender];
        require(currentAllowance >= amount, "ERC20: transfer amount exceeds allowance");
        _transfer(from, to, amount);
        _approve(from, msg.sender, currentAllowance - amount);
        return true;
    }

    function mint(address to, uint256 amount) public {
        _totalSupply += amount;
        _balances[to] += amount;
        emit Transfer(address(0), to, amount);
    }

    function _transfer(address from, address to, uint256 amount) internal {
        require(from != address(0), "ERC20: transfer from the zero address");
        require(to != address(0), "ERC20: transfer to the zero address");
        require(_balances[from] >= amount, "ERC20: transfer amount exceeds balance");
        _balances[from] -= amount;
        _balances[to] += amount;
        emit Transfer(from, to, amount);
    }

    function _approve(address owner, address spender, uint256 amount) internal {
        require(owner != address(0), "ERC20: approve from the zero address");
        require(spender != address(0), "ERC20: approve to the zero address");
        _allowances[owner][spender] = amount;
        emit Approval(owner, spender, amount);
    }
}

/**
 * @title DynamicPrecisionTest
 * @notice Test suite for dynamic precision handling in PrecisionLib
 */
contract DynamicPrecisionTest is Test {
    MockToken public usdc6; // 6 decimals (like USDC on Ethereum)
    MockToken public usdt18; // 18 decimals (like USDT on some chains)
    MockToken public dai18; // 18 decimals (like DAI)

    function setUp() public {
        usdc6 = new MockToken("USD Coin", "USDC", 6);
        usdt18 = new MockToken("Tether USD", "USDT", 18);
        dai18 = new MockToken("Dai Stablecoin", "DAI", 18);
    }

    function testGetPrecisionFactor() public {
        console.log("=== Testing getPrecisionFactor ===");
        
        assertEq(PrecisionLib.getPrecisionFactor(6), 1e6);
        assertEq(PrecisionLib.getPrecisionFactor(18), 1e18);
        assertEq(PrecisionLib.getPrecisionFactor(8), 1e8);
        
        console.log("getPrecisionFactor tests passed");
    }

    function testScaleStablecoinToToken() public {
        console.log("=== Testing scaleStablecoinToToken ===");
        
        // Test 6 decimals to 18 decimals (scale up)
        uint256 amount6 = 100 * 1e6; // 100 USDC (6 decimals)
        uint256 scaled6to18 = PrecisionLib.scaleStablecoinToToken(amount6, 6);
        assertEq(scaled6to18, 100 * 1e18); // Should be 100 * 1e18
        
        // Test 18 decimals to 18 decimals (no scaling)
        uint256 amount18 = 100 * 1e18; // 100 DAI (18 decimals)
        uint256 scaled18to18 = PrecisionLib.scaleStablecoinToToken(amount18, 18);
        assertEq(scaled18to18, 100 * 1e18); // Should remain the same
        
        console.log("6 decimals amount:", amount6);
        console.log("Scaled to 18 decimals:", scaled6to18);
        console.log("18 decimals amount:", amount18);
        console.log("Scaled (no change):", scaled18to18);
        
        console.log("scaleStablecoinToToken tests passed");
    }

    function testScaleTokenToStablecoin() public {
        console.log("=== Testing scaleTokenToStablecoin ===");
        
        // Test 18 decimals to 6 decimals (scale down)
        uint256 amount18 = 100 * 1e18; // 100 tokens (18 decimals)
        uint256 scaled18to6 = PrecisionLib.scaleTokenToStablecoin(amount18, 6);
        assertEq(scaled18to6, 100 * 1e6); // Should be 100 * 1e6
        
        // Test 18 decimals to 18 decimals (no scaling)
        uint256 scaled18to18 = PrecisionLib.scaleTokenToStablecoin(amount18, 18);
        assertEq(scaled18to18, 100 * 1e18); // Should remain the same
        
        console.log("18 decimals amount:", amount18);
        console.log("Scaled to 6 decimals:", scaled18to6);
        console.log("Scaled (no change):", scaled18to18);
        
        console.log("scaleTokenToStablecoin tests passed");
    }

    function testRoundTripConversion() public {
        console.log("=== Testing Round Trip Conversion ===");
        
        // Test 6 decimals round trip
        uint256 original6 = 123456789; // 123.456789 USDC
        uint256 scaled6to18 = PrecisionLib.scaleStablecoinToToken(original6, 6);
        uint256 backTo6 = PrecisionLib.scaleTokenToStablecoin(scaled6to18, 6);
        assertEq(backTo6, original6);
        
        // Test 18 decimals round trip
        uint256 original18 = 123456789012345678901; // 123.456789012345678901 DAI
        uint256 scaled18to18 = PrecisionLib.scaleStablecoinToToken(original18, 18);
        uint256 backTo18 = PrecisionLib.scaleTokenToStablecoin(scaled18to18, 18);
        assertEq(backTo18, original18);
        
        console.log("Original 6 decimals:", original6);
        console.log("After round trip:", backTo6);
        console.log("Original 18 decimals:", original18);
        console.log("After round trip:", backTo18);
        
        console.log("Round trip conversion tests passed");
    }

    function testCalculateStablecoinFeeFromWad() public {
        console.log("=== Testing calculateStablecoinFeeFromWad ===");
        
        uint256 wadFeeRate = 3 * 1e16; // 3% in WAD format
        
        // Test with 6 decimals stablecoin
        uint256 amount6 = 100 * 1e6; // 100 USDC
        uint256 fee6 = PrecisionLib.calculateStablecoinFeeFromWad(amount6, wadFeeRate, 6);
        uint256 expectedFee6 = 3 * 1e6; // 3% of 100 USDC = 3 USDC
        assertEq(fee6, expectedFee6);
        
        // Test with 18 decimals stablecoin
        uint256 amount18 = 100 * 1e18; // 100 DAI
        uint256 fee18 = PrecisionLib.calculateStablecoinFeeFromWad(amount18, wadFeeRate, 18);
        uint256 expectedFee18 = 3 * 1e18; // 3% of 100 DAI = 3 DAI
        assertEq(fee18, expectedFee18);
        
        console.log("6 decimals amount:", amount6);
        console.log("6 decimals fee:", fee6);
        console.log("18 decimals amount:", amount18);
        console.log("18 decimals fee:", fee18);
        
        console.log("calculateStablecoinFeeFromWad tests passed");
    }

    function testLegacyFunctionCompatibility() public {
        console.log("=== Testing Legacy Function Compatibility ===");
        
        uint256 amount = 100 * 1e18; // 100 tokens (assuming 18 decimals)
        uint256 wadFeeRate = 3 * 1e16; // 3% in WAD format
        
        // Test legacy function
        uint256 legacyFee = PrecisionLib.calculateUsdtFeeFromWad(amount, wadFeeRate);
        
        // Test new function with 18 decimals (should be equivalent)
        uint256 newFee = PrecisionLib.calculateStablecoinFeeFromWad(amount, wadFeeRate, 18);
        
        assertEq(legacyFee, newFee);
        
        console.log("Legacy fee:", legacyFee);
        console.log("New fee:", newFee);
        
        console.log("Legacy function compatibility tests passed");
    }

    function testEdgeCases() public {
        console.log("=== Testing Edge Cases ===");
        
        // Test zero amounts
        assertEq(PrecisionLib.scaleStablecoinToToken(0, 6), 0);
        assertEq(PrecisionLib.scaleTokenToStablecoin(0, 6), 0);
        
        // Test very small amounts
        uint256 smallAmount6 = 1; // 0.000001 USDC
        uint256 scaledSmall = PrecisionLib.scaleStablecoinToToken(smallAmount6, 6);
        assertEq(scaledSmall, 1e12); // Should be 1e12 in 18 decimals
        
        uint256 backToSmall = PrecisionLib.scaleTokenToStablecoin(scaledSmall, 6);
        assertEq(backToSmall, smallAmount6);
        
        console.log("Small amount (6 decimals):", smallAmount6);
        console.log("Scaled to 18 decimals:", scaledSmall);
        console.log("Scaled back:", backToSmall);
        
        console.log("Edge cases tests passed");
    }
}
