// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {UUPSUpgradeable} from "@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import {Factory} from "../src/Factory.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {UniversalTokenCollector} from "../src/UniversalTokenCollector.sol";
import {IPCoin} from "../src/IPCoin.sol";

/**
 * @title ComprehensiveMigrationV3_EN
 * @notice Comprehensive migration script - upgrade contracts and execute migration process
 * @dev Execute in the following order:
 *      1. Upgrade Factory contract
 *      2. Upgrade DistributionPool contract
 *      3. Execute migrateNewVersion() global migration
 *      4. Execute migrateCoinToUSDT() for existing coins
 */
contract ComprehensiveMigrationV3_EN is Script {
    // Environment variable configuration
    uint256 private deployerPrivateKey;
    address private factoryProxyAddress;
    address private poolProxyAddress;
    address private usdtAddress;
    address private currentAdmin; // Current admin address on chain
    address private withdrawAllowAddress;
    address private collectorProxyAddress;

    // Contract instances
    Factory private factory;
    DistributionPool private distributionPool;
    IERC20 private usdtToken;
    UniversalTokenCollector private collector;

    // Migration status tracking
    bool private factoryUpgraded = false;
    bool private poolUpgraded = false;
    bool private globalMigrated = false;
    bool private feeMigrated = false;
    bool private adminRightsGranted = false;

    uint256 private bnbPrice = 800;

    function setUp() public {
        // Read configuration from environment variables
        deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        factoryProxyAddress = vm.envAddress("FACTORY_PROXY_ADDRESS");
        poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");
        usdtAddress = vm.envAddress("USDT_ADDRESS");
        currentAdmin = vm.envAddress("CURRENT_ADMIN_ADDRESS"); // Current admin on chain
        withdrawAllowAddress = vm.envAddress("WITHDRW_ALLOW_ADDRESS");
        collectorProxyAddress = vm.envAddress("COLLECTOR_PROXY_ADDRESS");

        // Initialize contract instances
        factory = Factory(factoryProxyAddress);
        distributionPool = DistributionPool(payable(poolProxyAddress));
        usdtToken = IERC20(usdtAddress);
        collector = UniversalTokenCollector(collectorProxyAddress);

        console.log("=== Migration Script Configuration ===");
        console.log("Factory Proxy Address:", factoryProxyAddress);
        console.log("Pool Proxy Address:", poolProxyAddress);
        console.log("Usdt Address:", usdtAddress);
        console.log("withdrawAllow Address:", withdrawAllowAddress);
        console.log("Current Admin Address:", currentAdmin);
        console.log("Deployer Address:", vm.addr(deployerPrivateKey));
    }

    function run() public {
        vm.startBroadcast(deployerPrivateKey);

        console.log("=== Starting Comprehensive Migration Process ===");

        // Step 0: Grant admin rights to deployer using mock admin
        // grantAdminRights();

        // Step 1: Upgrade Factory contract
        // upgradeFactoryContract();

        // // Step 1.5: Set USDT address in Factory
        // setFactoryUsdtAddress();

        console.log("=== Migration Process Start ===");
        _printMigrationStart();

        // Step 2: Upgrade DistributionPool contract
        // upgradeDistributionPoolContract();

        // Step 2.5: Set USDT address in DistributionPool
        // setPoolUsdtAddrAndWithdrawAllow();

        // Step 3: Execute global migration
        // executeGlobalMigration();

        // Step 4: Migrate existing coins
        // migrateCoinToUSDT();

        // Step 5: Migrate platform fees to USDT
        // executeMigratePlatformFeesToUSDT();

        // Test buy/sell functionality for migrated coins
        // testMigratedCoins();

        // console.log("=== Migration Process Completed ===");
        // _printMigrationSummary();

        // console.log("=== UniversalTokenCollector Configuration ===");
        // collector.setAuthorizedWithdrawer(withdrawAllowAddress);

        vm.stopBroadcast();
    }

    /**
     * @notice Step 0: Grant admin rights to deployer using mock admin
     */
    function grantAdminRights() internal {
        console.log("=== Step 0: Granting Admin Rights to Deployer ===");

        address deployerAddress = vm.addr(deployerPrivateKey);

        // Check if deployer already has admin rights
        if (distributionPool.checkAdmin(deployerAddress)) {
            console.log("Deployer already has admin rights");
            adminRightsGranted = true;
            return;
        }

        // Verify current admin has admin rights
        require(distributionPool.checkAdmin(currentAdmin), "Current admin address does not have admin rights");

        console.log("Using mock admin to grant rights:", currentAdmin);
        console.log("Granting admin rights to:", deployerAddress);

        // Stop current broadcast to use prank
        vm.stopBroadcast();

        // Direct call instead of try-catch with this.
        performAdminGrant(deployerAddress);
        adminRightsGranted = true;
        console.log("Successfully granted admin rights to deployer");

        // Verify the grant was successful
        require(distributionPool.checkAdmin(deployerAddress), "Failed to verify admin rights grant");
        console.log("Admin rights verification passed");

        // Resume broadcast with deployer key
        vm.startBroadcast(deployerPrivateKey);
    }

    /**
     * @notice Execute admin grant logic
     */
    function performAdminGrant(
        address deployerAddress
    ) internal {
        // Use vm.prank to impersonate the current admin
        vm.prank(currentAdmin);
        distributionPool.addAdmin(deployerAddress);
        console.log("Admin rights granted to:", deployerAddress);
    }

    /**
     * @notice Step 1: Upgrade Factory contract
     */
    function upgradeFactoryContract() internal {
        console.log("=== Step 1: Upgrading Factory Contract ===");

        // Direct call instead of try-catch with this.
        performFactoryUpgrade();
        factoryUpgraded = true;
        console.log("Factory contract upgrade successful");
    }

    /**
     * @notice Execute Factory upgrade logic
     */
    function performFactoryUpgrade() internal {
        // Deploy new Factory implementation contract
        bytes32 factorySalt = keccak256(abi.encodePacked("Factory_Implementation_", block.timestamp));
        Factory newFactoryImplementation = new Factory{salt: factorySalt}();
        console.log("New Factory implementation address:", address(newFactoryImplementation));

        // Upgrade proxy contract
        UUPSUpgradeable factoryProxyContract = UUPSUpgradeable(factoryProxyAddress);
        factoryProxyContract.upgradeToAndCall(address(newFactoryImplementation), "");

        console.log("Factory proxy contract upgrade completed");
    }

    /**
     * @notice Step 1.5: Set USDT address in Factory
     */
    function setFactoryUsdtAddress() internal {
        console.log("=== Step 1.5: Setting Factory USDT Address ===");
        factory.initializeUsdtSupport(usdtAddress, 0);
        console.log("Factory USDT address set to:", usdtAddress);
    }

    /**
     * @notice Step 2: Upgrade DistributionPool contract
     */
    function upgradeDistributionPoolContract() internal {
        console.log("=== Step 2: Upgrading DistributionPool Contract ===");

        console.log("Before pause operations - paused:", distributionPool.paused());
        distributionPool.pause();

        // Direct call instead of try-catch with this.
        performPoolUpgrade();
        poolUpgraded = true;
        console.log("DistributionPool contract upgrade successful");
        console.log("After pool upgrade - paused:", distributionPool.paused());
    }

    /**
     * @notice Execute DistributionPool upgrade logic
     */
    function performPoolUpgrade() internal {
        // Deploy new DistributionPool implementation contract
        bytes32 poolSalt = keccak256(abi.encodePacked("DistributionPool_Implementation_", block.timestamp));
        DistributionPool newPoolImplementation = new DistributionPool{salt: poolSalt}();
        console.log("New DistributionPool implementation address:", address(newPoolImplementation));

        // Upgrade proxy contract
        UUPSUpgradeable poolProxyContract = UUPSUpgradeable(poolProxyAddress);
        poolProxyContract.upgradeToAndCall(address(newPoolImplementation), "");

        console.log("DistributionPool proxy contract upgrade completed");
    }

    /**
     * @notice Step 2.5: Set USDT address in DistributionPool
     */
    function setPoolUsdtAddrAndWithdrawAllow() internal {
        console.log("=== Step 2.5: Setting DistributionPool USDT Address ===");
        distributionPool.setUsdtToken(usdtAddress);
        console.log("DistributionPool USDT address set to:", usdtAddress);

        distributionPool.setAuthorizedIPHoldersFeesWithdrawer(withdrawAllowAddress);
        console.log("DistributionPool authorized IP holders fees withdrawer set to:", withdrawAllowAddress);

        console.log("After setUsdtToken - paused:", distributionPool.paused());
    }

    /**
     * @notice Step 3: Execute global migration
     */
    function executeGlobalMigration() internal {
        console.log("=== Step 3: Executing Global Migration ===");

        // Record pre-migration state
        _recordPreMigrationState();

        try distributionPool.migrateNewVersion() {
            globalMigrated = true;
            console.log("Global migration successful");
            console.log("After migrateNewVersion - paused:", distributionPool.paused());
            _recordPostMigrationState();
        } catch Error(string memory reason) {
            console.log("Global migration failed:", reason);
            if (keccak256(bytes(reason)) == keccak256(bytes("Already migrated to new version"))) {
                console.log("Contract already migrated to new version");
                globalMigrated = true;
            }
        } catch {
            console.log("Global migration failed: unknown error");
        }
    }

    /**
     * @notice Record pre-migration state
     */
    function _recordPreMigrationState() internal view {
        console.log("\n--- Pre-Migration State ---");
        console.log("Current version:", distributionPool.currentVersion());
        console.log("Total platform fee:", distributionPool.totalPlatformFee());
        console.log("Contract native balance:", address(distributionPool).balance);

        // Try to get USDT balance (if already set)
        try distributionPool.usdtToken() returns (IERC20Metadata poolUsdtToken) {
            if (address(poolUsdtToken) != address(0)) {
                console.log("Contract USDT balance:", poolUsdtToken.balanceOf(address(distributionPool)));
            }
        } catch {
            console.log("USDT token not yet set");
        }
    }

    /**
     * @notice Record post-migration state
     */
    function _recordPostMigrationState() internal view {
        console.log("\n--- Post-Migration State ---");
        console.log("Current version:", distributionPool.currentVersion());
        console.log("Total platform fee:", distributionPool.totalPlatformFee());
        console.log("Contract native balance:", address(distributionPool).balance);
        console.log("Contract USDT balance:", distributionPool.usdtToken().balanceOf(address(distributionPool)));

        if (address(distributionPool.usdtToken()) == address(0)) {
            console.log("USDT token not yet set");
            revert("USDT token not set");
        }
    }

    /**
     * @notice Step 4: Migrate existing coins to USDT
     */
    function migrateCoinToUSDT() internal {
        console.log("=== Step 4: Migrating Existing Coins to USDT ===");

        // Get coins to migrate based on actual requirements
        // Can be read from environment variables or configuration files
        address[] memory coinsToMigrate = _getCoinsToMigrate();

        if (coinsToMigrate.length == 0) {
            console.log("No coins to migrate");
            return;
        }

        console.log("Number of coins to migrate:", coinsToMigrate.length);

        for (uint256 i = 0; i < coinsToMigrate.length; i++) {
            _migrateSingleCoin(coinsToMigrate[i]);
        }
    }

    /**
     * @notice Get list of coins to migrate
     * @dev Can be modified based on actual requirements, e.g., read from environment variables or query on-chain data
     */
    function _getCoinsToMigrate() internal view returns (address[] memory) {
        // Try to read coin addresses from multiple environment variables
        address[] memory tempCoins = new address[](10); // Support up to 10 coins
        uint256 coinCount = 0;

        // Try to read COIN_TO_MIGRATE_1 to COIN_TO_MIGRATE_10
        for (uint256 i = 1; i <= 10; i++) {
            string memory envVar = string(abi.encodePacked("COIN_TO_MIGRATE_", vm.toString(i)));
            try vm.envAddress(envVar) returns (address coinAddress) {
                if (coinAddress != address(0)) {
                    tempCoins[coinCount] = coinAddress;
                    coinCount++;
                    console.log("Found coin to migrate", i, ":", coinAddress);
                }
            } catch {
                // If environment variable doesn't exist, skip
                break;
            }
        }

        // Create correctly sized array
        address[] memory coins = new address[](coinCount);
        for (uint256 i = 0; i < coinCount; i++) {
            coins[i] = tempCoins[i];
        }

        return coins;
    }

    /**
     * @notice Migrate single coin
     */
    function _migrateSingleCoin(
        address coinAddress
    ) internal {
        console.log("\n--- Migrating coin:", coinAddress, "---");

        // Check if coin is already migrated
        try distributionPool.isCoinMigratedToUSDT(coinAddress) returns (bool migrated) {
            if (migrated) {
                console.log("Coin already migrated:", coinAddress);
                return;
            }
        } catch {
            console.log("Cannot check coin migration status:", coinAddress);
            return;
        }

        // Execute coin migration
        uint256 deadline = block.timestamp + 300; // Expires in 5 minutes

        queryCoinStatus(coinAddress);
        try distributionPool.migrateCoinToUSDT(coinAddress, bnbPrice, deadline) {
            console.log("Coin migration successful:", coinAddress);
            console.log("After coin migration - paused:", distributionPool.paused());

            // Verify migration status
            bool migrated = distributionPool.isCoinMigratedToUSDT(coinAddress);
            require(migrated, "Coin migration status verification failed");
            console.log("Coin migration verification passed:", coinAddress);
        } catch Error(string memory reason) {
            console.log("Coin migration failed:", coinAddress);
            console.log("Failure reason:", reason);
        } catch {
            console.log("Coin migration failed with unknown error:", coinAddress);
        }
        queryCoinStatus(coinAddress);
    }

    /**
     * @notice Query coin status
     */
    function queryCoinStatus(
        address coinAddress
    ) internal view {
        console.log("=== Coin Status ===");
        console.log("Coin token sold:", distributionPool.getCoinTokenSold(coinAddress));
        console.log("Coin USDT amount:", distributionPool.getCoinUsdtAmount(coinAddress));
        console.log("Coin price constant:", distributionPool.getPriceConstant(coinAddress));
        console.log("Coin WAD price:", distributionPool.getCoinWADPrice(coinAddress));
        console.log("Contract native balance:", address(distributionPool).balance);
        console.log("Contract USDT balance:", usdtToken.balanceOf(address(distributionPool)));
        console.log("=== Coin Status ===");
    }

    /**
     * @notice Step 5: Migrate platform fee
     */
    function executeMigratePlatformFeesToUSDT() internal {
        console.log("=== Step 5: Executing Migrate Platform Fee ===");

        uint256 deadline = block.timestamp + 300; // Expires in 5 minutes

        console.log("Before platform fee migration - paused:", distributionPool.paused());
        try distributionPool.migratePlatformFeesToUSDT(bnbPrice, deadline) {
            feeMigrated = true;
            console.log("Platform fee migration successful");
            console.log("After platform fee migration - paused:", distributionPool.paused());
        } catch Error(string memory reason) {
            console.log("Platform fee migration failed:", reason);
        } catch {
            console.log("GlobPlatform fee migration failed: unknown error");
        }

        // Only unpause if currently paused
        if (distributionPool.paused()) {
            distributionPool.unpause();
            console.log("Contract unpaused successfully");
        } else {
            console.log("Contract is already unpaused");
        }
    }

    /**
     * @notice Test buy/sell functionality for migrated coins
     */
    function testMigratedCoins() internal {
        console.log("=== Step 6: Testing Buy/Sell Functionality ===");

        address deployerAddress = vm.addr(deployerPrivateKey);
        address[] memory coinsToTest = _getCoinsToMigrate();

        if (coinsToTest.length == 0) {
            console.log("No migrated coins to test");
            return;
        }

        // Print initial balances
        console.log("=== Initial Test Account Balances ===");
        console.log("Account USDT balance:", usdtToken.balanceOf(deployerAddress));
        console.log("Pool USDT balance:", usdtToken.balanceOf(address(distributionPool)));

        // Test each migrated coin
        for (uint256 i = 0; i < coinsToTest.length && i < 2; i++) {
            // Limit to 2 coins for testing
            _testSingleCoinBuySell(coinsToTest[i], deployerAddress);
        }

        // Print final balances
        console.log("=== Final Test Account Balances ===");
        console.log("Account USDT balance:", usdtToken.balanceOf(deployerAddress));
        console.log("Pool USDT balance:", usdtToken.balanceOf(address(distributionPool)));
    }

    /**
     * @notice Test buy/sell for a single coin
     */
    function _testSingleCoinBuySell(address coinAddress, address testAccount) internal {
        console.log("\n=== Testing Coin:", coinAddress, "===");

        IPCoin coin = IPCoin(coinAddress);
        uint256 testUsdtAmount = 4 * 1e18; // 4 USDT (18 decimals)

        // Check if account has enough USDT
        uint256 accountUsdtBefore = usdtToken.balanceOf(testAccount);
        if (accountUsdtBefore < testUsdtAmount) {
            console.log(
                "Insufficient USDT balance for testing. Required:", testUsdtAmount, "Available:", accountUsdtBefore
            );
            return;
        }

        // Step 1: Approve USDT for buy
        console.log("--- Step 1: Approving USDT for buy ---");
        usdtToken.approve(address(distributionPool), testUsdtAmount);
        console.log("USDT approved amount:", testUsdtAmount);

        // Step 2: Buy coins with USDT
        console.log("--- Step 2: Buying coins with USDT ---");
        uint256 coinBalanceBefore = coin.balanceOf(testAccount);
        console.log("Account coin balance before buy:", coinBalanceBefore);
        console.log("Account USDT balance before buy:", accountUsdtBefore);

        try distributionPool.buy(coinAddress, currentAdmin, testUsdtAmount, 0) {
            uint256 coinBalanceAfter = coin.balanceOf(testAccount);
            uint256 accountUsdtAfter = usdtToken.balanceOf(testAccount);
            uint256 coinsBought = coinBalanceAfter - coinBalanceBefore;
            uint256 usdtSpent = accountUsdtBefore - accountUsdtAfter;

            console.log("Buy successful!");
            console.log("USDT spent:", usdtSpent);
            console.log("Coins bought:", coinsBought);
            console.log("Account coin balance after buy:", coinBalanceAfter);
            console.log("Account USDT balance after buy:", accountUsdtAfter);

            // Step 3: Approve coins for sell
            console.log("--- Step 3: Approving coins for sell ---");
            coin.approve(address(distributionPool), coinsBought);
            console.log("Coins approved for sell:", coinsBought);

            // Step 4: Sell all bought coins
            console.log("--- Step 4: Selling all coins ---");
            uint256 usdtBalanceBeforeSell = usdtToken.balanceOf(testAccount);

            try distributionPool.sell(coinAddress, coinsBought, 0) {
                uint256 coinBalanceAfterSell = coin.balanceOf(testAccount);
                uint256 usdtBalanceAfterSell = usdtToken.balanceOf(testAccount);
                uint256 usdtReceived = usdtBalanceAfterSell - usdtBalanceBeforeSell;

                console.log("Sell successful!");
                console.log("Coins sold:", coinsBought);
                console.log("USDT received:", usdtReceived);
                console.log("Account coin balance after sell:", coinBalanceAfterSell);
                console.log("Account USDT balance after sell:", usdtBalanceAfterSell);

                // Calculate profit/loss
                if (usdtReceived > usdtSpent) {
                    console.log("Profit:", usdtReceived - usdtSpent, "USDT");
                } else {
                    console.log("Loss:", usdtSpent - usdtReceived, "USDT");
                }
            } catch Error(string memory reason) {
                console.log("Sell failed:", reason);
            } catch {
                console.log("Sell failed: unknown error");
            }
        } catch Error(string memory reason) {
            console.log("Buy failed:", reason);
        } catch {
            console.log("Buy failed: unknown error");
        }

        console.log("=== End testing coin:", coinAddress, "===\n");
    }

    /**
     * @notice Print migration before start
     */
    function _printMigrationStart() internal view {
        console.log("=== Migration Process Start ===");
        console.log("Current version:", distributionPool.currentVersion());
        console.log("Contract native balance:", address(distributionPool).balance);
        console.log("Contract USDT balance:", usdtToken.balanceOf(address(distributionPool)));
    }

    /**
     * @notice Print migration summary
     */
    function _printMigrationSummary() internal view {
        console.log("Admin rights grant status:", adminRightsGranted ? "Success" : "Failed");
        console.log("Factory upgrade status:", factoryUpgraded ? "Success" : "Failed");
        console.log("Pool upgrade status:", poolUpgraded ? "Success" : "Failed");
        console.log("Global migration status:", globalMigrated ? "Success" : "Failed");

        console.log("=== Final State After Migration ===");
        console.log("Current version:", distributionPool.currentVersion());
        console.log("Contract native balance:", address(distributionPool).balance);

        try distributionPool.usdtToken() returns (IERC20Metadata poolUsdtToken) {
            if (address(poolUsdtToken) != address(0)) {
                console.log("Contract USDT balance:", poolUsdtToken.balanceOf(address(distributionPool)));
            }
        } catch {
            console.log("USDT token information unavailable");
        }
    }
}
