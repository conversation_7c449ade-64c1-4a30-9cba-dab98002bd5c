// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract TestBuy is Script {
    function setUp() public {}

    function run() public {
        // 从环境变量读取配置
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

        // 交易参数 - 对应你的 cast 命令参数
        address coinAddress = 0x35E149718098AcF3eA5323a8CA7a24Fe34B0131a;
        address recipient = 0x6156CD0A43f78A2De0B6b963B9D866B8149983A7;
        uint256 usdtAmount = 100 * 1e18;
        uint256 minTokensToBuy = 0;

        console.log("=== Test Buy Transaction ===");
        console.log("Pool Proxy Address:", poolProxyAddress);
        console.log("Coin Address:", coinAddress);
        console.log("Recipient:", recipient);
        console.log("USDT Amount:", usdtAmount);
        console.log("Min Tokens To Buy:", minTokensToBuy);

        vm.startBroadcast(deployerPrivateKey);

        // 获取 DistributionPool 合约实例
        DistributionPool pool = DistributionPool(payable(poolProxyAddress));

        try pool.buy(coinAddress, recipient, usdtAmount, minTokensToBuy) {
            console.log("Buy transaction successful!");
        } catch Error(string memory reason) {
            console.log("Buy transaction failed with reason:", reason);
        } catch (bytes memory lowLevelData) {
            console.log("Buy transaction failed with low level error:");
            console.logBytes(lowLevelData);
        }

        vm.stopBroadcast();
    }
}
