// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {Factory} from "../src/Factory.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";

/**
 * @title VerifyMigrationStatus_EN
 * @notice Migration status verification script
 * @dev Used to check if contract upgrades and migrations completed successfully
 */
contract VerifyMigrationStatus_EN is Script {
    // Environment variable configuration
    address private factoryProxyAddress;
    address private poolProxyAddress;

    // Contract instances
    Factory private factory;
    DistributionPool private distributionPool;

    function setUp() public {
        // Read configuration from environment variables
        factoryProxyAddress = vm.envAddress("FACTORY_PROXY_ADDRESS");
        poolProxyAddress = vm.envAddress("POOL_PROXY_ADDRESS");

        // Initialize contract instances
        factory = Factory(factoryProxyAddress);
        distributionPool = DistributionPool(payable(poolProxyAddress));

        console.log("=== Migration Status Verification Script ===");
        console.log("Factory Proxy Address:", factoryProxyAddress);
        console.log("Pool Proxy Address:", poolProxyAddress);
    }

    function run() public view {
        console.log("=== Starting Migration Status Verification ===");

        // Verify basic contract functionality
        verifyContractAccess();

        // Verify migration status
        verifyMigrationStatus();

        // Verify coin migration status
        verifyCoinMigrationStatus();

        // Verify balance status
        verifyBalanceStatus();

        console.log("=== Verification Completed ===");
    }

    /**
     * @notice Verify contract access
     */
    function verifyContractAccess() internal view {
        console.log("=== Verifying Contract Access ===");

        // Verify Factory contract
        try factory.owner() returns (address owner) {
            console.log("Factory contract accessible");
            console.log("   Owner:", owner);
            console.log("   Contract address:", address(factory));
        } catch Error(string memory reason) {
            console.log("Factory contract access failed:", reason);
        } catch {
            console.log("Factory contract access failed: unknown error");
        }

        // Verify DistributionPool contract
        try distributionPool.currentVersion() returns (uint256 version) {
            console.log("DistributionPool contract accessible");
            console.log("   Current version:", version);
            console.log("   Contract address:", address(distributionPool));
        } catch Error(string memory reason) {
            console.log("DistributionPool contract access failed:", reason);
        } catch {
            console.log("DistributionPool contract access failed: unknown error");
        }
    }

    /**
     * @notice Verify migration status
     */
    function verifyMigrationStatus() internal view {
        console.log("=== Verifying Migration Status ===");

        try distributionPool.currentVersion() returns (uint256 version) {
            console.log("Current version:", version);

            if (version >= 2_025_081_901) {
                console.log("Already migrated to new version");
            } else {
                console.log("Not yet migrated to new version (expected: 2025081901)");
            }
        } catch {
            console.log("Cannot get version information");
        }

        // Check global parameters
        try distributionPool.bondingCurveTradeCap() returns (uint256 tradeCap) {
            console.log("Bonding curve trade cap:", tradeCap);

            uint256 expectedTradeCap = 400_000_000 * 1e18;
            if (tradeCap == expectedTradeCap) {
                console.log("Bonding curve trade cap updated");
            } else {
                console.log("Bonding curve trade cap not updated (expected:", expectedTradeCap, ")");
            }
        } catch {
            console.log("Cannot get bonding curve trade cap");
        }

        try distributionPool.minimumUsdtToBuy() returns (uint256 minUsdt) {
            console.log("Minimum USDT to buy:", minUsdt);

            uint256 expectedMinUsdt = 1 * 1e3; // 0.001 USDT
            if (minUsdt == expectedMinUsdt) {
                console.log("Minimum USDT to buy updated");
            } else {
                console.log("Minimum USDT to buy not updated (expected:", expectedMinUsdt, ")");
            }
        } catch {
            console.log("Cannot get minimum USDT to buy");
        }
    }

    /**
     * @notice Verify coin migration status
     */
    function verifyCoinMigrationStatus() internal view {
        console.log("=== Verifying Coin Migration Status ===");

        // Get list of coins to check
        address[] memory coinsToCheck = _getCoinsToCheck();

        if (coinsToCheck.length == 0) {
            console.log("No coins specified for checking");
            return;
        }

        console.log("Number of coins to check:", coinsToCheck.length);

        for (uint256 i = 0; i < coinsToCheck.length; i++) {
            _verifySingleCoinMigration(coinsToCheck[i]);
        }
    }

    /**
     * @notice Get list of coins to check
     */
    function _getCoinsToCheck() internal view returns (address[] memory) {
        // Try to read coin addresses from multiple environment variables
        address[] memory tempCoins = new address[](10);
        uint256 coinCount = 0;

        // Try to read COIN_TO_MIGRATE_1 to COIN_TO_MIGRATE_10
        for (uint256 i = 1; i <= 10; i++) {
            string memory envVar = string(abi.encodePacked("COIN_TO_MIGRATE_", vm.toString(i)));
            try vm.envAddress(envVar) returns (address coinAddress) {
                if (coinAddress != address(0)) {
                    tempCoins[coinCount] = coinAddress;
                    coinCount++;
                }
            } catch {
                // If environment variable doesn't exist, skip
                break;
            }
        }

        // Create correctly sized array
        address[] memory coins = new address[](coinCount);
        for (uint256 i = 0; i < coinCount; i++) {
            coins[i] = tempCoins[i];
        }

        return coins;
    }

    /**
     * @notice Verify single coin migration status
     */
    function _verifySingleCoinMigration(
        address coinAddress
    ) internal view {
        console.log("\n--- Checking coin:", coinAddress, "---");

        // Check if coin is migrated
        try distributionPool.isCoinMigratedToUSDT(coinAddress) returns (bool migrated) {
            if (migrated) {
                console.log("Coin migrated to USDT");
            } else {
                console.log("Coin not yet migrated to USDT");
            }
        } catch {
            console.log("Cannot check coin migration status");
            return;
        }

        // Check coin pool version - use getCoinPoolBasicData for simpler access
        try distributionPool.getCoinPoolBasicData(coinAddress) returns (
            address ipCoinContract, address creatorNftContract, bool allowTrade
        ) {
            console.log("   IP Coin Contract:", ipCoinContract);
            console.log("   Creator NFT Contract:", creatorNftContract);
            console.log("   Allow Trade:", allowTrade);
        } catch {
            console.log("Cannot get coin pool basic data");
        }

        // Get USDT amount in pool
        try distributionPool.getCoinUsdtAmount(coinAddress) returns (uint256 usdtAmount) {
            console.log("   USDT amount in pool:", usdtAmount);
        } catch {
            console.log("Cannot get USDT amount in pool");
        }
    }

    /**
     * @notice Verify balance status
     */
    function verifyBalanceStatus() internal view {
        console.log("=== Verifying Balance Status ===");

        // Check contract native token balance
        uint256 nativeBalance = address(distributionPool).balance;
        console.log("Contract native balance:", nativeBalance);

        // Check contract USDT balance
        try distributionPool.usdtToken() returns (IERC20Metadata usdtToken) {
            if (address(usdtToken) != address(0)) {
                uint256 usdtBalance = usdtToken.balanceOf(address(distributionPool));
                console.log("Contract USDT balance:", usdtBalance);
                console.log("USDT token address:", address(usdtToken));

                if (usdtBalance > 0) {
                    console.log("Contract holds USDT");
                } else {
                    console.log("Contract has no USDT balance");
                }
            } else {
                console.log("USDT token address not set");
            }
        } catch {
            console.log("Cannot get USDT token information");
        }

        // Check total platform fee
        try distributionPool.totalPlatformFee() returns (uint256 totalFee) {
            console.log("Total platform fee:", totalFee);

            if (totalFee > 0) {
                console.log("Platform fee exists");
            } else {
                console.log("No platform fee");
            }
        } catch {
            console.log("Cannot get total platform fee");
        }
    }

    /**
     * @notice Generate migration status report
     */
    function generateReport() external view {
        console.log("=== Migration Status Report ===");

        // Basic information
        console.log("Report generation time:", block.timestamp);
        console.log("Block height:", block.number);

        // Contract version information
        try distributionPool.currentVersion() returns (uint256 version) {
            console.log("DistributionPool version:", version);
        } catch {
            console.log("DistributionPool version: unavailable");
        }

        // Migration completion assessment
        uint256 migrationScore = _calculateMigrationScore();
        console.log("Migration completion score:", migrationScore, "/ 100");

        if (migrationScore >= 90) {
            console.log("Migration status: Excellent");
        } else if (migrationScore >= 70) {
            console.log("Migration status: Good");
        } else if (migrationScore >= 50) {
            console.log("Migration status: Needs attention");
        } else {
            console.log("Migration status: Issues exist");
        }
    }

    /**
     * @notice Calculate migration completion score
     */
    function _calculateMigrationScore() internal view returns (uint256) {
        uint256 score = 0;

        // Version check (30 points)
        try distributionPool.currentVersion() returns (uint256 version) {
            if (version >= 2_025_081_901) {
                score += 30;
            }
        } catch {}

        // Parameter check (30 points)
        try distributionPool.bondingCurveTradeCap() returns (uint256 tradeCap) {
            if (tradeCap == 400_000_000 * 1e18) {
                score += 15;
            }
        } catch {}

        try distributionPool.minimumUsdtToBuy() returns (uint256 minUsdt) {
            if (minUsdt == 1 * 1e3) {
                score += 15;
            }
        } catch {}

        // USDT setup check (20 points)
        try distributionPool.usdtToken() returns (IERC20Metadata usdtToken) {
            if (address(usdtToken) != address(0)) {
                score += 20;
            }
        } catch {}

        // Contract access check (20 points)
        try factory.owner() returns (address) {
            score += 10;
        } catch {}

        try distributionPool.currentVersion() returns (uint256) {
            score += 10;
        } catch {}

        return score;
    }
}
