// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.26;

import {UUPSUpgradeable} from "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import {Initializable} from "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import {PausableUpgradeable} from "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import {ReentrancyGuardUpgradeable} from "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import {AccessControlUpgradeable} from "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import {FixedPointMathLib} from "@solady/utils/FixedPointMathLib.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {IERC721} from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import {IERC1822Proxiable} from "@openzeppelin/contracts/interfaces/draft-IERC1822.sol";
import {PrecisionLib} from "./libraries/PrecisionLib.sol";
import "./interfaces/IUniswapV2Router02.sol";
import "./interfaces/IUniswapV2Factory.sol";
import "./interfaces/IDistributionPool.sol";
import "./interfaces/ITokenDistributor.sol";
import "./interfaces/IDistributionPoolReader.sol";
import "./IPCoin.sol";
import "./TraderNft.sol";

/**
 * @title DistributionPool Contract
 * @notice This is a distribution pool contract for managing IP token trading and liquidity
 * @dev This contract implements token trading, liquidity management, and NFT interaction functionalities
 */
contract DistributionPool is
    Initializable,
    UUPSUpgradeable,
    AccessControlUpgradeable,
    ReentrancyGuardUpgradeable,
    PausableUpgradeable,
    IDistributionPool,
    ITokenDistributor,
    IDistributionPoolReader
{
    using FixedPointMathLib for uint256;
    using SafeERC20 for IERC20;
    using SafeERC20 for IERC20Metadata;
    using SafeERC20 for IPCoin;

    // Role definitions
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    bytes32 public constant ADDIP_ROLE = keccak256("ADDIP_ROLE");

    // System constants
    address private constant BLACK_HOLE = 0x000000000000000000000000000000000000dEaD;
    uint256 private constant SQRT_PRECISION = 1e9;
    uint256 private constant DEFAULT_PRECISION = 1e18;

    // Variables use mixedCase naming convention
    // System parameters
    uint256 public bondingCurveTradeCap; // Maximum bonding curve token supply
    uint256 public priceConstant; // Price constant k
    uint256 private tradeFeeRatio; // Trading fee ratio
    uint256 private liquidityTokenAmount; // Liquidity token amount
    uint256 private liquidityUsdtAmount; // Liquidity USDT amount
    uint256 public _deprecated_virtualTokenSupply; // Virtual token supply
    uint256 public virtualUsdtSupply; // Virtual USDT supply
    uint256 public minimumUsdtToBuy; // Minimum USDT amount for buying

    // Address configurations
    address private feeReceiver; // Fee receiver address
    address private factoryAddr; // Factory contract address
    address private uniswapFactory; // Uniswap factory address
    address private uniswapRouter; // Uniswap router address

    // Maximum percentage of wallet balance that can be distributed (expressed in basis points)
    // 1000 basis points = 10%
    uint256 public maxWalletDistributionPercentage;

    // Exponential decay mining parameters
    uint256 public totalMiningSupply;
    uint256 public decayConstant;
    uint256 public decayPrecision;
    uint256 public initialWeeklyReward;

    uint256 public totalPlatformFee; // Total accumulated USDT fees (for tracking)
    uint256 public currentVersion;

    // Global reward interval (default: 1 week)
    uint256 public globalRewardInterval;

    // Main mapping for all coin pool data
    mapping(address => CoinPoolData) public coinPoolsData;

    // Mapping to track last reward received timestamp by recipient for a specific coin _coin -> recipient -> timestamp
    mapping(address => mapping(address => uint256)) public lastRewardReceivedTimestamp;

    // Whitelist for reward distribution
    mapping(address => bool) public creatorRewardDistributorsWhitelist;

    // Stablecoin token contract (added at the end to maintain storage compatibility)
    IERC20Metadata public usdtToken;

    // Migration state tracking (added at the end to maintain storage compatibility)
    mapping(address => bool) public coinMigratedToUSDT; // Track if a coin has been migrated to USDT

    // todo: 某个废弃设计的空间，可以留待以后使用
    mapping(address => mapping(address => uint256)) public _backupMap;

    // Fee distribution mapping for each coin
    mapping(address => FeeDistribution) public coinFeeDistributions;

    // Note: accumulatedHoldersFunds mapping has been moved to UniversalTokenCollector contract
    // This slot is kept for storage compatibility
    mapping(address => uint256) private _deprecated_accumulatedHoldersFunds;

    // Address authorized to withdraw IP holders fees
    address public authorizedIPHoldersFeesWithdrawer;

    // Struct for reward distribution
    struct RewardDistribution {
        address recipient;
        uint256 amount;
    }

    // Coin-specific parameters struct
    struct CoinParameters {
        uint256 liquidityTokenAmount; // Liquidity token amount
        uint256 bondingCurveTradeCap; // Maximum bonding curve token supply
        uint256 tradeFeeRatio; // Trading fee ratio
        uint256 minimumUsdtToBuy; // Minimum USDT amount to buy
        uint256 maxWalletDistributionPercentage; // Maximum percentage of wallet balance that can be distributed
        uint256 _deprecated_defaultRewardInterval; // @dev 暂时没有用到这个字段，之前的一个设计疏忽,暂时保留这个 slot 用于以后使用 Default reward interval (from globalRewardInterval)
        uint256 totalMiningSupply; // Total mining supply
        uint256 decayConstant; // Decay constant for exponential decay mining
        uint256 decayPrecision; // Precision for decay constant
        uint256 initialWeeklyReward; // Initial weekly reward
        uint256 liquidityUsdtAmount; // Liquidity USDT amount
        uint256 virtualUsdtSupply; // Virtual USDT supply
        uint256 _deprecated_virtualTokenSupply; // Virtual token supply
        uint256 priceConstant; // Price constant k
    }

    // Fee Distribution Struct
    struct FeeDistribution {
        uint256 platformFee; // 平台收入累积 (50%)
        uint256 ipHoldersUnclaimed; // IP币拥有者未提取 (20%)
        uint256 rewardPoolUnclaimed; // 奖励池未提取 (10%)
        uint256 creatorRewardsTotal; // 创作者奖励累积分配量 (20%)
    }

    // Coin Pool Data Struct
    struct CoinPoolData {
        IPCoin ipCoinContract; // IP token contract
        TraderNft traderNftContract; // Trader NFT contract
        address creatorNftContract; // Creator NFT contract
        bool allowTrade; // Token trading permission
        bool tradeNftStatus; // NFT trading status
        address pairAddress; // Token pair address
        address aiWallet; // AI wallet address
        uint256 poolVersion; // Pool version (previously ipVersions)
        uint256 tokenAmountSold; // Token amount in pool (previously tokensAmounts)
        uint256 usdtAmountInPool; // USDT amount in pool (previously nativeAmountInPool)
        uint256 intervalRewardDistributionTotal; // Interval reward distribution total
        uint256 epochCreatorRewardDistributionTotal; // Epoch creator reward distribution total
        uint256 lastDistributionTimestamp; // Last distribution timestamp
        uint256 lastCreatorDistributionTimestamp; // Last creator distribution timestamp
        uint256 cumulativeDistribution; // Cumulative distribution
        uint256 remainingSupply; // Remaining supply for exponential decay mining
        uint256 creationTimestamp; // Creation timestamp
        uint256 specificRewardInterval; // Specific reward interval (previously rewardIntervals)
        CoinParameters parameters; // Coin-specific parameters
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Initialize the contract
     * @param _admin Admin address
     * @param _feeReceiver Fee receiver address
     * @param factory Factory contract address
     * @param _usdtToken USDT token contract address
     */
    function initialize(address _admin, address _feeReceiver, address factory, address _usdtToken) public initializer {
        __Pausable_init();
        __AccessControl_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        _grantRole(ADMIN_ROLE, _admin);
        _grantRole(PAUSER_ROLE, _admin);
        _grantRole(ADDIP_ROLE, factory);

        feeReceiver = _feeReceiver;
        factoryAddr = factory;

        // Set stablecoin token
        require(_usdtToken != address(0), "Stablecoin address cannot be zero");
        usdtToken = IERC20Metadata(_usdtToken);

        // 设置通用参数
        _setCommonParameters();

        // 设置特定链的参数
        _setChainSpecificParameters();
    }

    // ------------------------ Stablecoin Precision Functions -----------------------------------

    /**
     * @notice Get the decimals of the stablecoin token
     * @return Number of decimals the stablecoin uses
     */
    function getStablecoinDecimals() public view returns (uint8) {
        return usdtToken.decimals();
    }

    /**
     * @notice Convert an amount from internal 18-decimal representation to stablecoin precision
     * @param amount Amount in 18-decimal precision
     * @return Amount in stablecoin precision
     */
    function convertToStablecoinPrecision(
        uint256 amount
    ) public view returns (uint256) {
        uint8 stablecoinDecimals = getStablecoinDecimals();
        return PrecisionLib.scaleTokenToStablecoin(amount, stablecoinDecimals);
    }

    /**
     * @notice Convert an amount from stablecoin precision to internal 18-decimal representation
     * @param amount Amount in stablecoin precision
     * @return Amount in 18-decimal precision
     */
    function convertFromStablecoinPrecision(
        uint256 amount
    ) public view returns (uint256) {
        uint8 stablecoinDecimals = getStablecoinDecimals();
        return PrecisionLib.scaleStablecoinToToken(amount, stablecoinDecimals);
    }

    // ------------------------ User Functions -----------------------------------

    /**
     * @notice Buy tokens with USDT
     * @param _coin Token address
     * @param recipient Recipient address
     * @param usdtAmount Amount of USDT to spend
     * @param minTokensToBuy Minimum amount of tokens to buy
     */
    function buy(
        address _coin,
        address recipient,
        uint256 usdtAmount,
        uint256 minTokensToBuy
    ) external whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-buy checks
        require(poolData.allowTrade, FeatureDisabled());
        require(poolData.tokenAmountSold <= poolData.parameters.bondingCurveTradeCap, MaxCapReached());
        // Convert minimum buy amount to stablecoin precision for comparison
        uint8 stablecoinDecimals = getStablecoinDecimals();
        uint256 minimumInStablecoinPrecision =
            PrecisionLib.scaleTokenToStablecoin(poolData.parameters.minimumUsdtToBuy, stablecoinDecimals);
        require(usdtAmount >= minimumInStablecoinPrecision, MinimumTransactionLimit());
        if (recipient == address(this) || recipient == poolData.pairAddress) revert InvalidRecipient();
        require(coinMigratedToUSDT[_coin], CoinNotMigrated());

        // Check USDT allowance and balance
        require(usdtToken.allowance(msg.sender, address(this)) >= usdtAmount, InsufficientAllowance());
        require(usdtToken.balanceOf(msg.sender) >= usdtAmount, InsufficientBalance());

        // Transfer USDT from buyer
        usdtToken.safeTransferFrom(msg.sender, address(this), usdtAmount);

        // Calculate token amount to buy
        uint256 tokensToBuy = calculateTokenAmount(_coin, usdtAmount);
        // Confirm token amount is valid
        require(tokensToBuy > 0, InvalidAmount());
        require(tokensToBuy >= minTokensToBuy, SlippageExceeded());
        require(poolData.tokenAmountSold + tokensToBuy <= poolData.parameters.bondingCurveTradeCap, MaxCapReached());

        // Calculate buyer fee using dynamic precision handling (reuse stablecoinDecimals from above)
        uint256 buyerFee = PrecisionLib.calculateStablecoinFeeFromWad(
            usdtAmount, poolData.parameters.tradeFeeRatio, stablecoinDecimals
        );

        // Update token amounts
        poolData.tokenAmountSold = poolData.tokenAmountSold + tokensToBuy;
        poolData.usdtAmountInPool = poolData.usdtAmountInPool + usdtAmount - buyerFee;

        // Distribute trading fees (this may immediately transfer creator fees)
        _distributeFee(_coin, buyerFee);

        // Store token ID before potential state changes
        uint256 tokenId = 0;

        // Disable trading if bonding curve is 100% complete
        if (poolData.tokenAmountSold >= poolData.parameters.bondingCurveTradeCap) {
            addLiquidityToPool(_coin);
        }

        // If Trader NFT is enabled, mint token and record transaction
        if (poolData.tradeNftStatus) {
            tokenId = poolData.traderNftContract.safeMint(recipient, tokensToBuy);
        } else {
            // Use safe transfer pattern to prevent reentrancy
            require(poolData.ipCoinContract.transfer(recipient, tokensToBuy), TransferFailed());
        }

        emit Buy(msg.sender, recipient, _coin, tokensToBuy, usdtAmount, tokenId, poolData.tradeNftStatus);
    }

    /**
     * @notice Sell tokens for USDT
     * @param _coin Token address
     * @param tokenAmount Amount of tokens to sell
     * @param minUsdtToReturn Minimum USDT amount to return
     */
    function sell(address _coin, uint256 tokenAmount, uint256 minUsdtToReturn) external whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-sell checks
        require(coinMigratedToUSDT[_coin], CoinNotMigrated());
        require(poolData.allowTrade, FeatureDisabled());
        require(tokenAmount > 0, InvalidAmount());
        // todo: 可以看看这个限制能否去除，但还是要以安全性为第一优先考虑
        require(tokenAmount > SQRT_PRECISION, InvalidAmount());

        uint256 userCoinBalance = poolData.ipCoinContract.balanceOf(msg.sender);
        require(userCoinBalance >= tokenAmount, InsufficientBalance());

        uint256 allowance = poolData.ipCoinContract.allowance(msg.sender, address(this));
        require(allowance >= tokenAmount, InsufficientAllowance());

        // Check if pool has enough tokens
        require(poolData.tokenAmountSold >= tokenAmount, "Insufficient pool token balance");

        (uint256 usdtToReturn, uint256 sellerFee) = calcUsdtAmountSell(_coin, tokenAmount);

        // Confirm USDT amount to return is valid
        require(usdtToReturn >= minUsdtToReturn, SlippageExceeded());

        // ADD: More comprehensive USDT balance check including fees
        uint256 totalUsdtNeeded = usdtToReturn + sellerFee;
        require(poolData.usdtAmountInPool >= totalUsdtNeeded, "Insufficient pool USDT");
        require(usdtToken.balanceOf(address(this)) >= totalUsdtNeeded, InvalidAmount());

        // Update token amounts
        poolData.tokenAmountSold = poolData.tokenAmountSold - tokenAmount;
        // First, only deduct the amount returned to seller
        poolData.usdtAmountInPool = poolData.usdtAmountInPool - usdtToReturn - sellerFee;

        // Distribute trading fees (this may immediately transfer creator fees)
        _distributeFee(_coin, sellerFee);

        // transfer tokens from seller to pool
        require(poolData.ipCoinContract.transferFrom(msg.sender, address(this), tokenAmount), TransferFailed());

        // Transfer USDT to seller
        usdtToken.safeTransfer(msg.sender, usdtToReturn);

        emit Sell(_coin, msg.sender, tokenAmount, usdtToReturn);
    }

    /**
     * @notice Add liquidity to Uniswap pool with USDT
     * @param _coin Token address
     * @return Liquidity pool address
     */
    function addLiquidityToPool(
        address _coin
    ) internal whenNotPaused returns (address) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-liquidity checks
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        require(poolData.allowTrade, FeatureDisabled());

        // Convert liquidity USDT amount to stablecoin precision for comparison
        uint8 stablecoinDecimals = getStablecoinDecimals();
        uint256 liquidityUsdtInStablecoinPrecision =
            PrecisionLib.scaleTokenToStablecoin(poolData.parameters.liquidityUsdtAmount, stablecoinDecimals);

        require(poolData.usdtAmountInPool >= liquidityUsdtInStablecoinPrecision, InsufficientLiquidity());
        require(usdtToken.balanceOf(address(this)) >= liquidityUsdtInStablecoinPrecision, InsufficientBalance());
        require(poolData.tokenAmountSold == poolData.parameters.bondingCurveTradeCap, InsufficientBalance());

        // Set token as listed BEFORE adding liquidity to allow transfers to pair
        poolData.ipCoinContract.setListed();

        // Approve tokens and USDT for liquidity
        poolData.ipCoinContract.approve(uniswapRouter, poolData.parameters.liquidityTokenAmount);
        usdtToken.approve(uniswapRouter, liquidityUsdtInStablecoinPrecision);
        IUniswapV2Router02 router = IUniswapV2Router02(uniswapRouter);

        // Add liquidity with USDT
        router.addLiquidity(
            _coin,
            address(usdtToken),
            poolData.parameters.liquidityTokenAmount,
            liquidityUsdtInStablecoinPrecision,
            poolData.parameters.liquidityTokenAmount, // slippage is unavoidable
            liquidityUsdtInStablecoinPrecision, // slippage is unavoidable
            BLACK_HOLE,
            block.timestamp + 300
        );

        poolData.allowTrade = false;
        // Collect listing fee in USDT
        // @dev When the Bonding curve reaches 100%, the part that exceeds the expected USDT amount is the listing fee
        totalPlatformFee = totalPlatformFee + (poolData.usdtAmountInPool - liquidityUsdtInStablecoinPrecision);
        // Update token amounts
        poolData.usdtAmountInPool = 0;
        poolData.tokenAmountSold = poolData.tokenAmountSold + poolData.parameters.liquidityTokenAmount;

        emit TokenListedToDex(_coin, poolData.pairAddress);

        return poolData.pairAddress;
    }

    // ------------------------ Bonding Curve Compute Logic -----------------------------------

    /**
     * @notice Calculate token amount for buying
     * @param _coin Token address
     * @param _usdtAmount Amount of USDT to spend
     * @return Token amount
     */
    function calculateTokenAmount(address _coin, uint256 _usdtAmount) public view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        if (_usdtAmount == 0) return 0;
        if (!poolData.allowTrade) {
            return 0;
        }

        uint8 stablecoinDecimals = getStablecoinDecimals();
        uint256 buyerFee = PrecisionLib.calculateStablecoinFeeFromWad(
            _usdtAmount, poolData.parameters.tradeFeeRatio, stablecoinDecimals
        );

        uint256 usdtAmountAfterFee = _usdtAmount - buyerFee;

        // Convert virtual USDT supply to stablecoin precision for calculation
        uint256 virtualUsdtInStablecoinPrecision =
            PrecisionLib.scaleTokenToStablecoin(poolData.parameters.virtualUsdtSupply, stablecoinDecimals);
        uint256 virtualU = virtualUsdtInStablecoinPrecision + poolData.usdtAmountInPool;

        // Calculate token amount using virtual reserves
        uint256 rawTokenAmount = _rawCalculateTokens(virtualU, usdtAmountAfterFee, poolData.parameters.priceConstant);

        // Check max token cap
        if (rawTokenAmount + poolData.tokenAmountSold > poolData.parameters.bondingCurveTradeCap) {
            rawTokenAmount = poolData.parameters.bondingCurveTradeCap - poolData.tokenAmountSold;
        }

        return rawTokenAmount;
    }

    /**
     * @notice Calculate raw token amount using Bonding Curve
     * @dev Uses square root formula, handles dynamic stablecoin precision
     * @param currentStablecoin Current stablecoin amount (in stablecoin precision)
     * @param dStablecoin Stablecoin amount (in stablecoin precision)
     * @param kWad Price constant for the calculation
     * @return Token amount
     */
    function _rawCalculateTokens(
        uint256 currentStablecoin,
        uint256 dStablecoin,
        uint256 kWad // WAD 标度的 priceConstant
    ) private view returns (uint256) {
        // Convert stablecoin amounts to 18 decimal precision for calculation
        uint8 stablecoinDecimals = getStablecoinDecimals();
        uint256 u18_before = PrecisionLib.scaleStablecoinToToken(currentStablecoin, stablecoinDecimals);
        uint256 u18_after = PrecisionLib.scaleStablecoinToToken(currentStablecoin + dStablecoin, stablecoinDecimals);

        // Calculate square root difference (result is in 9 decimal precision)
        uint256 sqrtDiff = FixedPointMathLib.sqrt(u18_after) - FixedPointMathLib.sqrt(u18_before);

        // Convert sqrt result to 18 decimal precision and apply bonding curve formula
        // Formula: tokens = 2 * sqrt_diff / k
        uint256 scaledSqrtDiff = PrecisionLib.scaleSqrtToToken(sqrtDiff);
        return FixedPointMathLib.divWad(scaledSqrtDiff * 2, kWad);
    }

    function calcUsdtAmountSell(
        address _coin,
        uint256 _tokenAmount
    ) public view returns (uint256 dUsdtNet, uint256 feeUsdt) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        if (!poolData.allowTrade) {
            return (0, 0);
        }

        // Convert virtual USDT supply to stablecoin precision for calculation
        uint8 stablecoinDecimals = getStablecoinDecimals();
        uint256 virtualUsdtInStablecoinPrecision =
            PrecisionLib.scaleTokenToStablecoin(poolData.parameters.virtualUsdtSupply, stablecoinDecimals);
        uint256 virtualU = virtualUsdtInStablecoinPrecision + poolData.usdtAmountInPool;

        // Convert virtual stablecoin amount to 18 decimal precision for calculation
        uint256 U18_before = PrecisionLib.scaleStablecoinToToken(virtualU, stablecoinDecimals);

        // Calculate square root (result is in 9 decimal precision)
        uint256 sqrtBefore = FixedPointMathLib.sqrt(U18_before);

        // Calculate delta sqrt: deltaSqrt = (K/2) * tokenAmount
        // K is in WAD, tokenAmount is in 18 decimals => result is in 18 decimals
        uint256 deltaSqrt18 = FixedPointMathLib.mulWad(_tokenAmount, poolData.parameters.priceConstant) / 2;

        // Convert to sqrt precision (9 decimals) to align with sqrtBefore
        uint256 deltaSqrt = PrecisionLib.scaleTokenToSqrt(deltaSqrt18);

        require(deltaSqrt <= sqrtBefore, "sell too large");
        uint256 sqrtAfter = sqrtBefore - deltaSqrt;
        uint256 U18_after = sqrtAfter * sqrtAfter; // Back to 18 decimal precision
        uint256 dU18 = U18_before - U18_after;
        uint256 dUsdtGross = PrecisionLib.scaleTokenToStablecoin(dU18, stablecoinDecimals);

        feeUsdt = PrecisionLib.calculateStablecoinFeeFromWad(
            dUsdtGross, poolData.parameters.tradeFeeRatio, stablecoinDecimals
        );
        dUsdtNet = dUsdtGross - feeUsdt;

        return (dUsdtNet, feeUsdt);
    }

    // ------------------------ Public Query Functions -----------------------------------

    /**
     * @notice Calculate USDT amount for selling tokens
     * @param _coin Token address
     * @param _tokenAmount Amount of tokens to sell
     * @return USDT amount
     */
    function calculateUsdtAmount(address _coin, uint256 _tokenAmount) public view returns (uint256) {
        (uint256 usdtAmount,) = calcUsdtAmountSell(_coin, _tokenAmount);
        return usdtAmount;
    }

    /**
     * @notice Get coin's USDT amount
     * @param _coin Token address
     * @return Coin's USDT amount
     */
    function getCoinUsdtAmount(
        address _coin
    ) external view returns (uint256) {
        return coinPoolsData[_coin].usdtAmountInPool;
    }

    /**
     * @notice Get token's trading status
     * @param _coin Token address
     * @return Token's trading status
     */
    function getCoinTradeStatus(
        address _coin
    ) external view returns (bool) {
        return coinPoolsData[_coin].allowTrade;
    }

    /**
     * @notice Get token's total amount sold
     * @param _coin Token address
     * @return Token's total amount sold
     */
    function getCoinTokenSold(
        address _coin
    ) external view returns (uint256) {
        return coinPoolsData[_coin].tokenAmountSold;
    }

    function getPriceConstant(
        address _coin
    ) external view returns (uint256) {
        return coinPoolsData[_coin].parameters.priceConstant;
    }

    /**
     * @notice Get token's price
     * @dev Returns token's price in USDT
     * @param _coin Token address
     * @return Token's price
     */
    function getCoinWADPrice(
        address _coin
    ) public view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        (uint256 usdtAmountInPool,) = getCapAndSupply(_coin);

        return priceWad(usdtAmountInPool, poolData.parameters.priceConstant);
    }

    /**
     * @notice Get token's capacity and supply
     * @dev Returns total capacity and supply, including virtual supply
     * @param _coin Token address
     * @return Token's capacity and supply
     */
    function getCapAndSupply(
        address _coin
    ) public view returns (uint256, uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Convert amounts to stablecoin precision
        uint8 stablecoinDecimals = getStablecoinDecimals();

        if (poolData.tokenAmountSold >= poolData.parameters.bondingCurveTradeCap) {
            // Convert liquidity USDT amount to stablecoin precision
            uint256 liquidityUsdtInStablecoinPrecision =
                PrecisionLib.scaleTokenToStablecoin(poolData.parameters.liquidityUsdtAmount, stablecoinDecimals);
            return (liquidityUsdtInStablecoinPrecision * 10, poolData.ipCoinContract.totalSupply());
        }
        // Convert virtual USDT supply to stablecoin precision
        uint256 virtualUsdtInStablecoinPrecision =
            PrecisionLib.scaleTokenToStablecoin(poolData.parameters.virtualUsdtSupply, stablecoinDecimals);
        return (poolData.usdtAmountInPool + virtualUsdtInStablecoinPrecision, poolData.ipCoinContract.totalSupply());
    }

    function priceWad(uint256 stablecoinInPool, uint256 kWad) internal view returns (uint256) {
        // Convert stablecoin amount to 18 decimal precision for calculation
        uint8 stablecoinDecimals = getStablecoinDecimals();
        uint256 U18 = PrecisionLib.scaleStablecoinToToken(stablecoinInPool, stablecoinDecimals);
        uint256 sqrtU1e9 = FixedPointMathLib.sqrt(U18); // sqrt 输出隐含 1e9
        // 把 sqrtU 提升到 WAD，再乘以 k（WAD），返回 WAD 价格
        return FixedPointMathLib.mulWad(kWad, sqrtU1e9 * 1e9); // = k * sqrt(U)
    }

    /**
     * @notice Get fee receiver address
     * @return Fee receiver address
     */
    function getFeeReceiver() external view returns (address) {
        return feeReceiver;
    }

    /**
     * @notice Get coin pool basic data for a specific token
     * @param coin Address of the IP coin
     * @return ipCoinContract Address of the IP coin contract
     * @return creatorNftContract Address of the creator NFT contract
     * @return allowTrade Whether trading is allowed
     */
    function getCoinPoolBasicData(
        address coin
    ) external view returns (address ipCoinContract, address creatorNftContract, bool allowTrade) {
        CoinPoolData storage poolData = coinPoolsData[coin];
        return (address(poolData.ipCoinContract), poolData.creatorNftContract, poolData.allowTrade);
    }

    /**
     * @notice Check if a coin exists in the pool
     * @param coin Address of the IP coin
     * @return True if coin exists, false otherwise
     */
    function coinExists(
        address coin
    ) external view returns (bool) {
        return address(coinPoolsData[coin].ipCoinContract) != address(0);
    }

    /**
     * @notice Get current week number for a specific coin pool
     * @dev Calculates the current week number based on pool creation timestamp
     * @param _coin IPCoin address
     * @return currentWeekNumber Current week number (1-based)
     */
    function getCurrentWeekNumber(
        address _coin
    ) external view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Handle legacy data migration - if creationTimestamp is not set or too small (possibly legacy data)
        uint256 creationTimestamp = poolData.creationTimestamp;
        if (creationTimestamp <= 100) {
            creationTimestamp = block.timestamp;
        }

        // Calculate current week number using partial first week + calendar week approach
        // Calculate which day of the week the creation time is (0=Monday, 6=Sunday)
        uint256 creationWeekday = (creationTimestamp / 86_400 + 4) % 7; // 0-6, 0 is Monday

        // Calculate the end time of the creation week's Sunday
        uint256 daysUntilSunday = (6 - creationWeekday); // Days until Sunday
        uint256 endOfFirstWeek = (creationTimestamp / 86_400) * 86_400 + daysUntilSunday * 86_400 + 86_399; // Sunday 23:59:59

        if (block.timestamp <= endOfFirstWeek) {
            // Still in the first week (partial week)
            return 1;
        } else {
            // Past the first week, calculate subsequent full weeks
            uint256 timeAfterFirstWeek = block.timestamp - endOfFirstWeek - 1;
            uint256 fullWeeksPassed = timeAfterFirstWeek / 604_800; // Full weeks
            return 2 + fullWeeksPassed; // First week + passed full weeks
        }
    }

    /**
     * @notice Check if account has admin role
     * @param account Account address
     * @return If account has admin role
     */
    function checkAdmin(
        address account
    ) external view returns (bool) {
        return hasRole(ADMIN_ROLE, account);
    }

    /**
     * @notice Set maximum percentage of wallet balance that can be distributed
     * @dev Only callable by admin role
     * @param percentage New maximum percentage in basis points (100 = 1%)
     */
    function setMaxWalletDistributionPercentage(
        uint256 percentage
    ) external onlyRole(ADMIN_ROLE) {
        require(percentage > 0 && percentage <= 10_000, "Invalid percentage"); // Max 100% (10000 basis points)
        maxWalletDistributionPercentage = percentage;
        emit MaxWalletDistributionPercentageUpdated(percentage);
    }

    /**
     * @notice Update coin-specific parameters
     * @dev Only callable by admin role
     * @param _coin Token address
     * @param _liquidityTokenAmount Liquidity token amount
     * @param _bondingCurveTradeCap Maximum bonding curve token supply
     * @param _tradeFeeRatio Trading fee ratio
     * @param _minimumUsdtToBuy Minimum USDT amount to buy
     * @param _maxWalletDistributionPercentage Maximum percentage of wallet balance that can be distributed
     * @param _totalMiningSupply Total mining supply
     * @param _decayConstant Decay constant for exponential decay mining
     * @param _decayPrecision Precision for decay constant
     * @param _initialWeeklyReward Initial weekly reward
     * @param _liquidityUsdtAmount Liquidity USDT amount
     * @param _virtualUsdtSupply Virtual USDT supply
     * @param _priceConstant Price constant k
     */
    function setCoinParameters(
        address _coin,
        uint256 _liquidityTokenAmount,
        uint256 _bondingCurveTradeCap,
        uint256 _tradeFeeRatio,
        uint256 _minimumUsdtToBuy,
        uint256 _maxWalletDistributionPercentage,
        uint256 _totalMiningSupply,
        uint256 _decayConstant,
        uint256 _decayPrecision,
        uint256 _initialWeeklyReward,
        uint256 _liquidityUsdtAmount,
        uint256 _virtualUsdtSupply,
        uint256 _priceConstant
    ) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        poolData.parameters.liquidityTokenAmount = _liquidityTokenAmount;
        poolData.parameters.bondingCurveTradeCap = _bondingCurveTradeCap;
        poolData.parameters.tradeFeeRatio = _tradeFeeRatio;
        poolData.parameters.minimumUsdtToBuy = _minimumUsdtToBuy;
        poolData.parameters.maxWalletDistributionPercentage = _maxWalletDistributionPercentage;
        poolData.parameters.totalMiningSupply = _totalMiningSupply;
        poolData.parameters.decayConstant = _decayConstant;
        poolData.parameters.decayPrecision = _decayPrecision;
        poolData.parameters.initialWeeklyReward = _initialWeeklyReward;
        poolData.parameters.liquidityUsdtAmount = _liquidityUsdtAmount;
        poolData.parameters.virtualUsdtSupply = _virtualUsdtSupply;
        poolData.parameters.priceConstant = _priceConstant;

        emit CoinParametersUpdated(_coin);
    }

    // ------------------------ Admin Functions -----------------------------------
    /**
     * @notice Add IP token
     * @dev Only callable by ADDIP_ROLE
     * @param _coin Token address
     */
    function addIp(address _coin, address _creatorNft, address _aiAgentWallet) external onlyRole(ADDIP_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) == address(0), CoinAlreadyExists());

        poolData.ipCoinContract = IPCoin(_coin);
        poolData.tokenAmountSold = 0;
        poolData.usdtAmountInPool = 0;
        poolData.allowTrade = true;
        poolData.tradeNftStatus = false;
        poolData.creatorNftContract = _creatorNft;
        poolData.aiWallet = _aiAgentWallet;
        poolData.poolVersion = currentVersion;
        poolData.creationTimestamp = block.timestamp;
        poolData.parameters.liquidityTokenAmount = liquidityTokenAmount;
        poolData.parameters.bondingCurveTradeCap = bondingCurveTradeCap;
        poolData.parameters.tradeFeeRatio = tradeFeeRatio;
        poolData.parameters.minimumUsdtToBuy = minimumUsdtToBuy;
        poolData.parameters.maxWalletDistributionPercentage = maxWalletDistributionPercentage;
        poolData.parameters.totalMiningSupply = totalMiningSupply;
        poolData.parameters.decayConstant = decayConstant;
        poolData.parameters.decayPrecision = decayPrecision;
        poolData.parameters.initialWeeklyReward = initialWeeklyReward;
        poolData.parameters.liquidityUsdtAmount = liquidityUsdtAmount;
        poolData.parameters.virtualUsdtSupply = virtualUsdtSupply;
        poolData.parameters.priceConstant = priceConstant;

        // 初始化指数衰减挖矿参数
        poolData.remainingSupply = poolData.parameters.totalMiningSupply;

        address pair = IUniswapV2Factory(uniswapFactory).createPair(_coin, address(usdtToken));
        poolData.pairAddress = pair;

        poolData.ipCoinContract.mint(address(this), 1_000_000_000 * DEFAULT_PRECISION);

        poolData.ipCoinContract.initPair(pair);

        coinMigratedToUSDT[_coin] = true;

        emit CoinAdded(_coin, pair);
    }

    /**
     * @notice Set token's NFT status
     * @dev Only callable by ADMIN_ROLE
     * @param _coin Token address
     * @param _nft NFT contract address
     */
    function addTraderNft(address _coin, address _nft) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        poolData.traderNftContract = TraderNft(_nft);
        poolData.tradeNftStatus = true;

        emit TraderNftAdded(_coin, _nft);
    }

    /**
     * @notice Set fee receiver address
     * @dev Only callable by ADMIN_ROLE
     * @param _feeReceiver Fee receiver address
     */
    function setFeeReceiver(
        address _feeReceiver
    ) external onlyRole(ADMIN_ROLE) {
        feeReceiver = _feeReceiver;
    }

    /**
     * @notice Withdraw USDT fees
     * @dev Only callable by admin role
     */
    function withdrawUsdtFees() external nonReentrant onlyRole(ADMIN_ROLE) {
        if (totalPlatformFee == 0) {
            return;
        }
        uint256 contractBalance = usdtToken.balanceOf(address(this));
        require(contractBalance >= totalPlatformFee, InvalidAmount());

        usdtToken.safeTransfer(feeReceiver, totalPlatformFee);
        totalPlatformFee = 0;
    }

    /**
     * @notice Update factory contract address
     * @dev Only callable by ADMIN_ROLE
     * @param factory New factory contract address
     */
    function setFactory(
        address factory
    ) external onlyRole(ADMIN_ROLE) {
        require(factory != address(0), "Factory address cannot be zero");
        if (hasRole(ADDIP_ROLE, factoryAddr)) {
            _revokeRole(ADDIP_ROLE, factoryAddr);
        }
        factoryAddr = factory;
        _grantRole(ADDIP_ROLE, factoryAddr);
    }

    function removeAdmin(
        address _oldAdmin
    ) external onlyRole(ADMIN_ROLE) {
        require(_oldAdmin != address(0), "Invalid admin address");
        require(_oldAdmin != msg.sender, "You cannot remove yourself");

        _revokeRole(ADMIN_ROLE, _oldAdmin);
    }

    function addAdmin(
        address _newAdmin
    ) external onlyRole(ADMIN_ROLE) {
        _grantRole(ADMIN_ROLE, _newAdmin);
    }

    function addPauser(
        address _newPauser
    ) external onlyRole(ADMIN_ROLE) {
        _grantRole(PAUSER_ROLE, _newPauser);
    }

    function removePauser(
        address _oldPauser
    ) external onlyRole(ADMIN_ROLE) {
        _revokeRole(PAUSER_ROLE, _oldPauser);
    }

    /**
     * @notice Set minimum USDT to buy
     * @dev Only callable by ADMIN_ROLE
     * @param _minimumToBuy New minimum USDT amount
     */
    function setMinimumUsdtToBuy(
        uint256 _minimumToBuy
    ) external onlyRole(ADMIN_ROLE) {
        minimumUsdtToBuy = _minimumToBuy;
    }

    /**
     * @notice Set liquidity USDT amount
     * @dev Only callable by ADMIN_ROLE
     * @param _liquidityUsdtAmount New liquidity USDT amount
     */
    function setLiquidityUsdtAmount(
        uint256 _liquidityUsdtAmount
    ) external onlyRole(ADMIN_ROLE) {
        liquidityUsdtAmount = _liquidityUsdtAmount;
    }

    /**
     * @notice Pause contract
     * @dev Only callable by PAUSER_ROLE
     */
    function pause() external onlyRole(PAUSER_ROLE) {
        _pause();
    }

    /**
     * @notice Unpause contract
     * @dev Only callable by PAUSER_ROLE
     */
    function unpause() external onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    function migratePlatformFeesToUSDT(uint256 bnbPrice, uint256 deadline) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (coinMigratedToUSDT[address(this)]) {
            revert();
        }
        uint256 nativeFee = totalPlatformFee; // 旧 totalEthFee（BNB），同槽位
        if (nativeFee == 0) return;
        if (address(this).balance < nativeFee) revert InsufficientNativeBalance();

        uint256 got = _swapNativeToUSDT(nativeFee, bnbPrice * nativeFee, deadline);
        totalPlatformFee = got; // 现在是 USDT(6)
        coinMigratedToUSDT[address(this)] = true;

        emit PlatformFeeSwap(nativeFee, got);
    }

    /**
     * @notice Migrate contract to new version
     * @dev Only callable by ADMIN_ROLE
     */
    function migrateNewVersion() external onlyRole(ADMIN_ROLE) {
        uint256 newVersion = 2_025_082_001;

        uint256 oldVersion = currentVersion;
        if (currentVersion >= newVersion) revert AlreadyMigratedToNewVersion();

        // 设置通用参数
        _setCommonParameters();

        currentVersion = newVersion;

        // 设置特定链的参数
        _setChainSpecificParameters();

        emit PoolVersionUpdated(oldVersion, newVersion);
    }

    // ------------------------ Reward Distribution Functions -----------------------------------

    /**
     * @notice Add an address to the reward distributors whitelist
     * @dev Only callable by ADMIN_ROLE
     * @param distributor Address to add to whitelist
     */
    function addRewardDistributor(
        address distributor
    ) external onlyRole(ADMIN_ROLE) {
        creatorRewardDistributorsWhitelist[distributor] = true;
        emit RewardDistributorAdded(distributor);
    }

    /**
     * @notice Remove an address from the reward distributors whitelist
     * @dev Only callable by ADMIN_ROLE
     * @param distributor Address to remove from whitelist
     */
    function removeRewardDistributor(
        address distributor
    ) external onlyRole(ADMIN_ROLE) {
        creatorRewardDistributorsWhitelist[distributor] = false;
        emit RewardDistributorRemoved(distributor);
    }

    /**
     * @notice Set reward interval for a specific coin
     * @dev Only callable by ADMIN_ROLE, when set to 0 it will use the global interval
     * @param _coin IPCoin address
     * @param interval New reward interval in seconds, or 0 to use global interval
     */
    function setRewardInterval(address _coin, uint256 interval) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        poolData.specificRewardInterval = interval;
        emit RewardIntervalUpdated(_coin, interval);
    }

    /**
     * @notice Get reward interval for a specific coin
     * @param _coin IPCoin address
     * @return Reward interval in seconds
     */
    function getRewardInterval(
        address _coin
    ) external view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        return poolData.specificRewardInterval;
    }

    /**
     * @notice Get the virtual USDT supply for a specific coin
     * @param _coin IPCoin address
     * @return Virtual USDT supply
     */
    function getCoinVirtualUsdtSupply(
        address _coin
    ) external view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        return poolData.parameters.virtualUsdtSupply;
    }

    /**
     * @notice Set the global reward interval that applies to all coins without specific intervals
     * @dev Only callable by ADMIN_ROLE
     * @param interval New global reward interval in seconds
     */
    function setGlobalRewardInterval(
        uint256 interval
    ) external onlyRole(ADMIN_ROLE) {
        require(interval > 0, "Interval must be greater than zero");
        globalRewardInterval = interval;
        emit GlobalRewardIntervalUpdated(interval);
    }

    /**
     * @notice Get the global reward interval that applies to all coins without specific intervals
     * @return Global reward interval in seconds
     */
    function getGlobalRewardInterval() external view returns (uint256) {
        return globalRewardInterval;
    }

    /**
     * @notice Get the weekly output limit for a specific coin and week number
     * @dev Calculates the maximum weekly output based on exponential decay algorithm
     * @dev Formula: Rw = R1 × (1-k)^(w-1) where R1 is initial weekly reward, k is decay constant
     * @dev Alternative: Rw = k × Sw-1 where Sw-1 = S0 × (1-k)^(w-1) is remaining supply at week start
     * @param _coin IPCoin address
     * @param weekNumber Week number (1-based) to query the output limit for
     * @return weeklyOutputLimit Maximum output limit for the specified week
     */
    function getWeeklyOutputLimit(
        address _coin,
        uint256 weekNumber
    ) external view returns (uint256 weeklyOutputLimit) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        require(weekNumber > 0, "Week number must be greater than 0");

        // Get decay parameters
        uint256 _decayConstant = poolData.parameters.decayConstant; // k (e.g., 5530 for 0.00553)
        uint256 _decayPrecision = poolData.parameters.decayPrecision; // precision (e.g., 1,000,000)
        uint256 _totalMiningSupply = poolData.parameters.totalMiningSupply; // S0 (initial supply)

        // Calculate using the closed-form formula: Rw = R1 × (1-k)^(w-1)
        // Where R1 = k × S0 (initial weekly reward)

        // Calculate (1-k) with precision
        // (1-k) = (decayPrecision - decayConstant) / decayPrecision
        uint256 decayFactor = _decayPrecision - _decayConstant; // (1-k) × precision

        // Calculate (1-k)^(w-1) using power function
        uint256 decayPower = _power(decayFactor, _decayPrecision, weekNumber - 1);

        // Calculate R1 = k × S0
        uint256 _initialWeeklyReward = (_totalMiningSupply * _decayConstant) / _decayPrecision;

        // Calculate Rw = R1 × (1-k)^(w-1)
        weeklyOutputLimit = (_initialWeeklyReward * decayPower) / _decayPrecision;

        return weeklyOutputLimit;
    }

    /**
     * @notice Calculate base^exponent with precision handling for decay calculations
     * @dev Uses FixedPointMathLib library for efficient fixed-point arithmetic and power calculation
     * @param base Base value (with precision)
     * @param precision Precision factor
     * @param exponent Exponent value
     * @return result Base^exponent with precision
     */
    function _power(uint256 base, uint256 precision, uint256 exponent) internal pure returns (uint256 result) {
        if (exponent == 0) {
            return precision; // base^0 = 1 (with precision)
        }

        // Use FixedPointMathLib's rpow function: rpow(x, y, b) calculates x^y denominated in base b
        // We want (base/precision)^exponent * precision
        // This is equivalent to base^exponent / precision^(exponent-1)
        result = FixedPointMathLib.rpow(base, exponent, precision);

        return result;
    }

    /**
     * @notice Distribute rewards to ai agent and creator
     * @dev Only callable by whitelisted distributors, once per interval per group
     * @param _coin IPCoin address
     * @param amount Amount to distribute
     */
    function distributeCreatorRewards(address _coin, uint256 amount) external whenNotPaused nonReentrant {
        // Check if caller is whitelisted
        require(creatorRewardDistributorsWhitelist[msg.sender], "Not authorized");

        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Check if time interval has passed for this group (uses coin-specific interval if set, otherwise uses coin's default)
        uint256 interval = poolData.specificRewardInterval > 0 ? poolData.specificRewardInterval : globalRewardInterval;
        require(
            block.timestamp > poolData.lastCreatorDistributionTimestamp + interval
                || poolData.lastCreatorDistributionTimestamp == 0,
            "Time interval limit: one distribution per interval"
        );

        // Check if mining has been exhausted
        require(poolData.remainingSupply > 0, "Mining supply exhausted");

        // Handle legacy data migration - if creationTimestamp is not set or too small (possibly legacy data)
        if (poolData.creationTimestamp <= 100) {
            poolData.creationTimestamp = block.timestamp;
        }

        // Get current week number using the dedicated method
        uint256 currentWeekNumber = this.getCurrentWeekNumber(_coin);
        // 计算最大允许输出（基于指数衰减算法）
        // 使用正确的指数衰减公式：Rw = R1 × (1-k)^(w-1)
        uint256 maxIntervalOutput = this.getWeeklyOutputLimit(_coin, currentWeekNumber);

        // Ensure we don't distribute more than what's available in remaining supply
        if (maxIntervalOutput > poolData.remainingSupply) {
            maxIntervalOutput = poolData.remainingSupply;
        }

        // Make sure distribution amount doesn't exceed the calculated maximum
        require(amount <= maxIntervalOutput, "Exceeds maximum weekly production rate");

        // Check if total distribution exceeds epoch limit
        uint256 currentEpochStart = block.timestamp - (block.timestamp % interval);

        // Reset epoch total if we're in a new epoch
        if (currentEpochStart > poolData.lastCreatorDistributionTimestamp) {
            poolData.epochCreatorRewardDistributionTotal = 0;
        }

        // Update epoch total and cumulative distribution
        poolData.epochCreatorRewardDistributionTotal += amount;
        poolData.cumulativeDistribution += amount;
        // 更新剩余供应量
        // Sw = Sw-1 - Rw (remaining supply = previous remaining supply - current week reward)
        poolData.remainingSupply -= amount;

        // 更新最后分配时间戳
        poolData.lastCreatorDistributionTimestamp = block.timestamp;

        // 获取创建者地址 - 通过NFT tokenId 0的持有者
        address creator;
        try IERC721(poolData.creatorNftContract).ownerOf(0) returns (address owner) {
            creator = owner;
        } catch {
            revert();
        }
        // 按照规则分配代币：60%给创建者，20%给平台，20%给AI钱包
        uint256 creatorAmount = (amount * 60) / 100; // 60% 创建者
        uint256 platformAmount = (amount * 20) / 100; // 20% 平台
        uint256 aiWalletAmount = amount - creatorAmount - platformAmount; // 20% AI钱包

        bool success;
        success = poolData.ipCoinContract.transfer(creator, creatorAmount);
        require(success, "Transfer failed");
        success = poolData.ipCoinContract.transfer(feeReceiver, platformAmount);
        require(success, "Transfer failed");
        success = poolData.ipCoinContract.transfer(poolData.aiWallet, aiWalletAmount);
        require(success, "Transfer failed");

        emit CreatorRewardsDistributed(msg.sender, _coin, amount);
        emit WeeklyMiningReward(_coin, currentWeekNumber, amount, poolData.remainingSupply);
    }

    /**
     * @notice Get AI wallet for a specific coin
     * @param _coin IPCoin address
     * @return AI wallet address
     */
    function getCoinAiWallet(
        address _coin
    ) external view returns (address) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        return poolData.aiWallet;
    }

    /**
     * @notice Distribute rewards to multiple addresses
     * @dev Only callable by whitelisted distributors, once per interval per group
     * @dev Limited to a maximum of maxWalletDistributionPercentage of current wallet balance
     * @param _coin IPCoin address
     * @param distributions Array of reward distributions (max 10 recipients)
     */
    function distributeRewards(
        address _coin,
        RewardDistribution[] calldata distributions
    ) external whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Check if caller is whitelisted
        require(poolData.aiWallet == msg.sender, "Not authorized");

        // Check maximum number of recipients
        require(distributions.length <= 10, "Too many recipients");

        // Calculate total distribution amount
        uint256 totalDistribution = 0;
        for (uint256 i = 0; i < distributions.length; i++) {
            totalDistribution += distributions[i].amount;
        }

        // 提前检查授权额度，避免在循环过程中因 allowance 不足而整体回滚
        uint256 allowance = poolData.ipCoinContract.allowance(msg.sender, address(this));
        if (allowance < totalDistribution) revert AllowanceInsufficient();

        // Define the distribution interval, used for periodic limits like interval total
        uint256 interval = poolData.specificRewardInterval > 0 ? poolData.specificRewardInterval : globalRewardInterval;

        // Check if total distribution exceeds interval limit
        uint256 currentEpochStart = block.timestamp - (block.timestamp % interval);

        // Reset interval total if we're in a new interval
        if (currentEpochStart > poolData.lastDistributionTimestamp) {
            poolData.intervalRewardDistributionTotal = 0;
        }

        // Check if total distribution exceeds maximum percentage of wallet balance
        uint256 currentWalletBalance = poolData.ipCoinContract.balanceOf(msg.sender);
        uint256 maxDistributionAmount =
            (currentWalletBalance * poolData.parameters.maxWalletDistributionPercentage) / 10_000; // Convert basis points to percentage

        require(totalDistribution < maxDistributionAmount, "Exceeds maximum wallet distribution percentage");

        // Update interval total
        poolData.intervalRewardDistributionTotal += totalDistribution;

        // Update last distribution timestamp
        poolData.lastDistributionTimestamp = block.timestamp;

        // Distribute rewards
        for (uint256 i = 0; i < distributions.length; i++) {
            require(distributions[i].amount > 0, "Invalid amount");

            // Check if recipient has already received reward in this interval
            // @dev 打赏时关闭限制
            // https://applink.larksuite.com/client/message/link/open?token=AmfzPuycAAAmaHevjpfAwCA%3D
            // Check if recipient has already received reward in this interval
            // require(
            //     lastRewardReceivedTimestamp[_coin][distributions[i].recipient] < currentEpochStart,
            //     RecipientRewardedThisInterval()
            // );

            poolData.ipCoinContract.transferFrom(msg.sender, distributions[i].recipient, distributions[i].amount);

            // Update the last reward received timestamp for the recipient
            lastRewardReceivedTimestamp[_coin][distributions[i].recipient] = block.timestamp;
        }

        emit RewardsDistributed(msg.sender, _coin, totalDistribution, distributions.length);
    }

    /**
     * @notice Set common parameters used by both initialize and migrateNewVersion functions
     * @dev This internal function sets parameters that are common to both functions
     */
    function _setCommonParameters() internal {
        liquidityTokenAmount = 100_000_000 * DEFAULT_PRECISION;
        bondingCurveTradeCap = 400_000_000 * DEFAULT_PRECISION;
        tradeFeeRatio = 3 * 1e16; // 3%
        minimumUsdtToBuy = 1 * 1e15; // 0.001 in 18 decimal precision (will be converted based on actual stablecoin decimals)
        currentVersion = 2_025_082_001;

        // Exponential decay mining model - constants are defined as immutable above
        // Set default maximum distribution to 10% of wallet balance
        maxWalletDistributionPercentage = 1000; // 10% in basis points
        // Set default global reward interval to 1 week
        globalRewardInterval = 1 weeks;

        // Exponential decay mining parameters
        totalMiningSupply = 500_000_000 * DEFAULT_PRECISION; // 500M tokens
        decayConstant = 5530; // k = 0.00553 represented as 5530/1000000
        decayPrecision = 1_000_000; // Precision for decay constant
        initialWeeklyReward = 2_765_000 * DEFAULT_PRECISION; // R1 = k × S0

        liquidityUsdtAmount = 6000 * 1e18; // 6,000 in 18 decimal precision (will be converted based on actual stablecoin decimals)
        virtualUsdtSupply = 30 * 1e18; // 30 in 18 decimal precision (will be converted based on actual stablecoin decimals)
        priceConstant = 366_812_000_000; // k k≈3.66812×10^-7 with 18 decimal places - 6377
    }

    /**
     * @notice Set chain-specific parameters based on the current blockchain
     * @dev This internal function sets parameters specific to each supported blockchain
     */
    function _setChainSpecificParameters() internal {
        // Set Uniswap addresses - use chain-specific default addresses
        // https://docs.base.org/docs/contracts/
        // https://developer.pancakeswap.finance/contracts/v2/factory-v2
        if (block.chainid == 8453) {
            // base mainnet
            uniswapFactory = 0x8909Dc15e40173Ff4699343b6eB8132c65e18eC6;
            uniswapRouter = 0x4752ba5DBc23f44D87826276BF6Fd6b1C372aD24;
        } else if (block.chainid == 84_532) {
            // Base Sepolia - using official base sepolia uniswap v2 addresses
            uniswapFactory = 0x7Ae58f10f7849cA6F5fB71b7f45CB416c9204b1e;
            uniswapRouter = 0x1689E7B1F10000AE47eBfE339a4f69dECd19F602;
        } else if (block.chainid == 97) {
            // bsc testnet
            uniswapFactory = 0x6725F303b657a9451d8BA641348b6761A6CC7a17;
            uniswapRouter = 0xD99D1c33F9fC3444f8101754aBC46c52416550D1;
        } else if (block.chainid == 56) {
            // bsc mainnet
            uniswapFactory = 0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73;
            uniswapRouter = 0x10ED43C718714eb63d5aA57B78B54704E256024E;
            usdtToken = IERC20Metadata(0x55d398326f99059fF775485246999027B3197955);
        } else {
            revert UnsupportedChain();
        }
    }

    // ==================== FEE DISTRIBUTION FUNCTIONS ====================

    /**
     * @notice Internal function to distribute trading fees
     * @param _coin Token address
     * @param totalFee Total fee amount to distribute
     */
    function _distributeFee(address _coin, uint256 totalFee) internal {
        FeeDistribution storage feeData = coinFeeDistributions[_coin];

        // Calculate each part of the fee (using direct percentages)
        uint256 creatorFee = (totalFee * 20) / 100; // 20%
        uint256 ipHoldersFee = (totalFee * 20) / 100; // 20%
        uint256 rewardPoolUnclaimed = (totalFee * 10) / 100; // 10%
        uint256 platformFee = totalFee - creatorFee - ipHoldersFee - rewardPoolUnclaimed; // 50%

        // Accumulate unclaimed fees for later withdrawal
        feeData.platformFee += platformFee;
        feeData.ipHoldersUnclaimed += ipHoldersFee;
        feeData.rewardPoolUnclaimed += rewardPoolUnclaimed;

        // Track total fees for reporting
        totalPlatformFee += platformFee;

        // Distribute creator fee immediately
        _distributeCreatorFeeReward(_coin, creatorFee);
        feeData.creatorRewardsTotal += creatorFee;
    }

    /**
     * @notice Internal function to distribute creator fee reward immediately
     * @param _coin Token address
     * @param amount Amount to distribute to creator
     */
    function _distributeCreatorFeeReward(address _coin, uint256 amount) internal {
        if (amount == 0) return;

        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Get creator wallet from CreatorNft contract (owner of token ID 0)
        address creator;
        try IERC721(poolData.creatorNftContract).ownerOf(0) returns (address owner) {
            creator = owner;
        } catch {
            // If failed to get creator, accumulate in platformFee instead
            coinFeeDistributions[_coin].platformFee += amount;
            return;
        }

        if (creator != address(0)) {
            usdtToken.safeTransfer(creator, amount);
            emit CreatorFeeRewardDistributed(_coin, creator, amount);
        } else {
            // If creator is zero address, accumulate in platformFee instead
            coinFeeDistributions[_coin].platformFee += amount;
        }
    }

    /**
     * @notice Withdraw IP holders fees for a specific coin (authorized withdrawer only)
     * @param _coin Token address
     * @param to Address to receive the fees
     */
    function withdrawIPHoldersFees(address _coin, address to) external nonReentrant {
        require(msg.sender == authorizedIPHoldersFeesWithdrawer, "Caller is not authorized IP holders fees withdrawer");
        require(to != address(0), "Invalid recipient address");

        FeeDistribution storage feeData = coinFeeDistributions[_coin];
        uint256 amount = feeData.ipHoldersUnclaimed;
        require(amount > 0, "No IP holders fees to withdraw");

        feeData.ipHoldersUnclaimed = 0;
        usdtToken.safeTransfer(to, amount);
        emit IPHoldersFeesWithdrawn(_coin, to, amount);
    }

    /**
     * @notice Withdraw reward pool fees for a specific coin
     * @param _coin Token address
     * @param to Address to receive the fees
     */
    function withdrawRewardPoolFees(address _coin, address to) external onlyRole(ADMIN_ROLE) nonReentrant {
        require(to != address(0), "Invalid recipient address");

        FeeDistribution storage feeData = coinFeeDistributions[_coin];
        uint256 amount = feeData.rewardPoolUnclaimed;
        require(amount > 0, "No reward pool fees to withdraw");

        feeData.rewardPoolUnclaimed = 0;
        usdtToken.safeTransfer(to, amount);
        emit RewardPoolFeesWithdrawn(_coin, to, amount);
    }

    /**
     * @notice Get fee distribution data for a specific coin
     * @param _coin Token address
     * @return Fee distribution data
     */
    function getCoinFeeDistribution(
        address _coin
    ) external view returns (FeeDistribution memory) {
        return coinFeeDistributions[_coin];
    }

    /**
     * @notice Set the authorized IP holders fees withdrawer address (admin only)
     * @param _authorizedWithdrawer The address authorized to withdraw IP holders fees
     */
    function setAuthorizedIPHoldersFeesWithdrawer(
        address _authorizedWithdrawer
    ) external onlyRole(ADMIN_ROLE) {
        require(_authorizedWithdrawer != address(0), "Invalid withdrawer address");

        address oldWithdrawer = authorizedIPHoldersFeesWithdrawer;
        authorizedIPHoldersFeesWithdrawer = _authorizedWithdrawer;

        emit AuthorizedIPHoldersFeesWithdrawerUpdated(oldWithdrawer, _authorizedWithdrawer);
    }

    // ==================== MIGRATION FUNCTIONS ====================

    // Events for migration
    event CoinMigratedToUSDT(address indexed coin, uint256 nativeAmountIn, uint256 usdtAmountOut);
    event CoinMigrationFailed(address indexed coin, string reason);
    event PoolVersionUpdated(uint256 oldVersion, uint256 newVersion);
    event PlatformFeeSwap(uint256 nativeAmountOut, uint256 usdtAmountIn);

    // Note: HoldersFunds events have been moved to UniversalTokenCollector contract

    // Events for fee distribution
    event CreatorFeeRewardDistributed(address indexed coin, address indexed creator, uint256 amount);
    event PlatformRevenueWithdrawn(address indexed coin, address indexed to, uint256 amount);
    event IPHoldersFeesWithdrawn(address indexed coin, address indexed to, uint256 amount);
    event AuthorizedIPHoldersFeesWithdrawerUpdated(address indexed oldWithdrawer, address indexed newWithdrawer);
    event RewardPoolFeesWithdrawn(address indexed coin, address indexed to, uint256 amount);

    // Errors for migration
    error CoinAlreadyMigrated();
    error CoinNotInBondingCurve();
    error MigrationInProgress();
    error InsufficientNativeBalance();
    error SwapFailed();
    error InvalidMinUsdtAmount();
    error CoinNotMigrated();
    error AlreadyMigratedToNewVersion();
    // General errors
    error AllowanceInsufficient();
    error DeadlineExpired();
    error ZeroAddress();
    error InvalidRecipient();

    /**
     * @notice Set USDT token contract address
     * @dev Only callable by admin
     * @param _usdtToken New USDT token contract address
     */
    function setUsdtToken(
        address _usdtToken
    ) external onlyRole(ADMIN_ROLE) {
        if (_usdtToken == address(0)) revert ZeroAddress();
        usdtToken = IERC20Metadata(_usdtToken);
    }

    /**
     * @notice Migrate a coin from native currency to USDT
     * @dev Only callable by admin, only for coins still in bonding curve
     * @param coin The coin address to migrate
     * @param bnbPrice BNB price in USDT（人类阅读友好精度）
     * @param deadline Deadline for the swap transaction
     */
    function migrateCoinToUSDT(
        address coin,
        uint256 bnbPrice,
        uint256 deadline
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (deadline < block.timestamp) revert DeadlineExpired();
        // Check if coin exists and is not already migrated
        CoinPoolData storage coinData = coinPoolsData[coin];
        if (address(coinData.ipCoinContract) == address(0)) revert CoinNotInBondingCurve(); // Coin doesn't exist
        if (coinMigratedToUSDT[coin]) revert CoinAlreadyMigrated();

        // Check if coin is still in bonding curve (not listed on Uniswap)
        require(coinData.allowTrade, FeatureDisabled());

        // Get the native currency reserve for this coin
        // Note: This assumes the old storage slot still contains native amount
        // In a real migration, we'd need to carefully handle the storage layout
        uint256 nativeReserve = coinData.usdtAmountInPool; // This was nativeAmountInPool before

        // Perform the swap from native to USDT
        uint256 usdtReceived;
        if (nativeReserve > 0) {
            // Ensure contract has enough native balance
            if (address(this).balance < nativeReserve) {
                revert InsufficientNativeBalance();
            }

            usdtReceived = _swapNativeToUSDT(nativeReserve, bnbPrice * nativeReserve, deadline);

            if (usdtReceived == 0) {
                revert SwapFailed();
            }

            // Update the coin's accounting to USDT
            coinData.usdtAmountInPool = usdtReceived;
        }

        // update other parameters
        coinData.poolVersion = currentVersion;
        coinData.parameters.minimumUsdtToBuy = minimumUsdtToBuy;
        coinData.parameters.liquidityUsdtAmount = liquidityUsdtAmount;
        coinData.parameters.virtualUsdtSupply = virtualUsdtSupply;
        coinData.parameters.priceConstant = priceConstant;

        // Mark coin as migrated
        coinMigratedToUSDT[coin] = true;

        emit CoinMigratedToUSDT(coin, nativeReserve, usdtReceived);
    }

    /*
     * @notice Migrate a pool to new version
     * @dev Only callable by admin, only for coins still in bonding curve
     * @param coin The coin address to migrate
     */
    function migratePoolNewVersion(
        address coin
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        // Check if coin exists
        CoinPoolData storage coinData = coinPoolsData[coin];
        if (address(coinData.ipCoinContract) == address(0)) revert CoinNotInBondingCurve(); // Coin doesn't exist

        coinData.poolVersion = currentVersion;
        coinData.parameters.minimumUsdtToBuy = minimumUsdtToBuy;
        coinData.parameters.liquidityUsdtAmount = liquidityUsdtAmount;
        coinData.parameters.virtualUsdtSupply = virtualUsdtSupply;
        coinData.parameters.priceConstant = priceConstant;
    }

    /**
     * @notice Internal function to swap native currency to USDT
     * @param nativeAmount Amount of native currency to swap
     * @param minUsdtOut Minimum USDT to receive
     * @param deadline Deadline for swap
     * @return usdtReceived Amount of USDT received
     */
    function _swapNativeToUSDT(
        uint256 nativeAmount,
        uint256 minUsdtOut,
        uint256 deadline
    ) internal returns (uint256 usdtReceived) {
        // Get WETH address based on chain
        address weth = IUniswapV2Router02(uniswapRouter).WETH();

        // Define swap path: ETH/BNB -> WETH/WBNB -> USDT
        address[] memory path = new address[](2);
        path[0] = weth;
        path[1] = address(usdtToken);

        // Perform the swap
        try IUniswapV2Router02(uniswapRouter).swapExactETHForTokens{value: nativeAmount}(
            minUsdtOut, path, address(this), deadline
        ) returns (uint256[] memory amounts) {
            usdtReceived = amounts[amounts.length - 1];
        } catch {
            revert SwapFailed();
        }
    }

    /**
     * @notice Check if a coin has been migrated to USDT
     * @param coin The coin address to check
     * @return bool True if migrated, false otherwise
     */
    function isCoinMigratedToUSDT(
        address coin
    ) external view returns (bool) {
        return coinMigratedToUSDT[coin];
    }

    /**
     * @notice Distribute collected tokens according to predefined ratios
     * @dev This function is deprecated - distribution logic has been moved to UniversalTokenCollector
     */
    function distributeCollectedTokens(address, /* cftAddress */ uint256 /* amount */ ) external pure {
        revert("Function deprecated - use UniversalTokenCollector for token distribution");
    }

    // receive() to accept ETH for migration swaps
    receive() external payable {
        require(msg.sender == uniswapRouter, "Only accept ETH from router");
    }

    // The following functions are overrides required by Solidity.
    function _authorizeUpgrade(
        address newImplementation
    ) internal view override onlyRole(ADMIN_ROLE) {
        // Prevent upgrade to zero address
        if (newImplementation == address(0)) revert ZeroAddress();

        // Verify the new implementation supports UUPS
        // This prevents upgrading to a non-upgradeable contract which would lock the proxy
        bytes32 expectedSlot = 0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc; // ERC1967 implementation slot
        try IERC1822Proxiable(newImplementation).proxiableUUID() returns (bytes32 uuid) {
            if (uuid != expectedSlot) {
                revert("Invalid implementation UUID");
            }
        } catch {
            revert("Implementation does not support UUPS");
        }
    }
}
