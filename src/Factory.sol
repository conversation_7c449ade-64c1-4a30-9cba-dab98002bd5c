// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.26;

import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC1822Proxiable} from "@openzeppelin/contracts/interfaces/draft-IERC1822.sol";
import "./IPCoin.sol";
import "./TraderNft.sol";
import "./CreatorNft.sol";
import "./DistributionPool.sol";

/**
 * @title Factory Contract
 * @notice This is a factory contract used to deploy and manage IP-related contracts
 * @dev This contract is responsible for deploying IPCoin, TraderNft, and CreatorNft contracts, and managing their implementation addresses
 */
contract Factory is Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    // Event triggered when a new IP contract is deployed
    event IPDeployed(address indexed creator, string ipRef, address ipCoin, address aiWallet, address creatorNft);

    // Event triggered when minimum buy value is updated
    event MinimumBuyValueUpdated(uint256 oldValue, uint256 newValue);

    // Event triggered when implementation addresses are updated
    event ImplementationsUpdated(
        address indexed oldTraderImpl,
        address indexed newTraderImpl,
        address indexed oldCreatorImpl,
        address newCreatorImpl
    );

    event UsdtAddressUpdated(address oldUsdt, address newUsdt, uint256 oldMinimumBuyValue, uint256 newMinimumBuyValue);

    // Event triggered when distribution pool address is updated
    event DistributionPoolUpdated(address indexed oldPool, address indexed newPool);

    error TraderNftNotDeployed();
    error MinimumTransactionLimit();
    error ZeroAddress();

    DistributionPool public distributionPool;

    address public admin;

    // Store implementation addresses for each contract
    address public traderNftImpl;
    address public creatorNftImpl;

    uint256 public minimumUsdtBuyValue;

    // New storage variables for USDT support (added for upgrade safety)
    IERC20 public usdt;

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Initialize the contract
     * @param admin_ Administrator address
     * @param creatorNftImpl_ Creator NFT implementation contract address
     * @param distributionPool_ Distribution pool contract address
     */
    function initialize(address admin_, address creatorNftImpl_, address distributionPool_) public initializer {
        if (admin_ == address(0)) revert ZeroAddress();
        if (creatorNftImpl_ == address(0)) revert ZeroAddress();
        if (distributionPool_ == address(0)) revert ZeroAddress();

        __Ownable_init(admin_);
        __ReentrancyGuard_init();
        admin = admin_;

        creatorNftImpl = creatorNftImpl_;

        address oldPool = address(distributionPool);
        distributionPool = DistributionPool(payable(distributionPool_));
        emit DistributionPoolUpdated(oldPool, distributionPool_);

        minimumUsdtBuyValue = 0; // Default minimum USDT buy value
    }

    /**
     * @notice Initialize USDT support (for contract upgrades)
     * @param usdt_ USDT token contract address
     * @param minimumUsdtBuyValue_ Minimum USDT buy value
     */
    function initializeUsdtSupport(address usdt_, uint256 minimumUsdtBuyValue_) external onlyOwner {
        if (address(usdt_) == address(0)) {
            revert ZeroAddress();
        }

        address oldUsdt = address(usdt);
        uint256 oldMinimumUsdtBuyValue = minimumUsdtBuyValue;
        // Only initialize once
        usdt = IERC20(usdt_);
        minimumUsdtBuyValue = minimumUsdtBuyValue_;
        emit UsdtAddressUpdated(oldUsdt, usdt_, oldMinimumUsdtBuyValue, minimumUsdtBuyValue_);
    }

    /**
     * @notice Deployment parameters structure without trader NFT with USDT support
     * @param coinName Token name
     * @param coinSymbol Token symbol
     * @param creator Creator address
     * @param aiAgentWallet AI Agent wallet address
     * @param creatorNftName Creator NFT name
     * @param creatorNftSymbol Creator NFT symbol
     * @param creatorNftURI Creator NFT URI
     * @param ipRef IP reference, used by backend to capture this value for business processing
     * @param usdtAmount USDT amount to buy initially
     */
    struct DeploymentWithUsdtParams {
        string coinName;
        string coinSymbol;
        address creator;
        address aiAgentWallet;
        string creatorNftName;
        string creatorNftSymbol;
        string creatorNftURI;
        string ipRef;
        uint256 usdtAmount;
    }

    /**
     * @notice Deploy IP-related contracts without trader NFT
     * @param params Deployment parameters
     */
    function deployIP(
        DeploymentWithUsdtParams memory params
    ) external payable nonReentrant returns (address) {
        require(params.usdtAmount >= minimumUsdtBuyValue, MinimumTransactionLimit());

        // Deploy IPCoin directly (non-upgradeable)
        IPCoin ipCoinInstance = new IPCoin(params.coinName, params.coinSymbol, address(distributionPool));
        address ipCoinAddress = address(ipCoinInstance);

        // Cache state variables to reduce redundant SLOADs
        DistributionPool pool = distributionPool;

        // CreatorNft is deployed as proxy with init data to save one external call
        bytes memory initData = abi.encodeWithSelector(
            CreatorNft.initialize.selector,
            admin,
            params.creator,
            address(pool),
            params.creatorNftName,
            params.creatorNftSymbol,
            params.creatorNftURI
        );
        address creatorNftProxyAddress = newERC1967Proxy(creatorNftImpl, initData);
        CreatorNft creatorNftInstance = CreatorNft(creatorNftProxyAddress);

        _initializeContractsWithUsdt(ipCoinInstance, creatorNftInstance, params, pool);

        emit IPDeployed(params.creator, params.ipRef, ipCoinAddress, params.aiAgentWallet, creatorNftProxyAddress);

        return ipCoinAddress;
    }

    /**
     * @notice Initialize IP token and creator NFT contracts with USDT
     * @param ipCoin IP token contract instance
     * @param creatorNft Creator NFT contract instance
     * @param params Deployment parameters including USDT amount
     */
    function _initializeContractsWithUsdt(
        IPCoin ipCoin,
        CreatorNft creatorNft,
        DeploymentWithUsdtParams memory params,
        DistributionPool pool
    ) internal {
        // Add IP into pool (creatorNft 已在 proxy 构造中初始化)
        pool.addIp(address(ipCoin), address(creatorNft), params.aiAgentWallet);

        if (params.usdtAmount > 0) {
            // Cache usdt state variable to reduce SLOADs
            IERC20 usdtToken = usdt;
            // Transfer USDT from user to this contract, then to distribution pool
            usdtToken.transferFrom(msg.sender, address(this), params.usdtAmount);
            usdtToken.approve(address(pool), params.usdtAmount);
            pool.buy(address(ipCoin), params.creator, params.usdtAmount, 0);
        }
    }

    /**
     * @notice Create new ERC1967 proxy contract
     * @param logicAddress_ Logic contract address
     * @return Proxy contract address
     */
    function newERC1967Proxy(address logicAddress_, bytes memory initData_) internal returns (address) {
        return address(new ERC1967Proxy(logicAddress_, initData_));
    }

    /**
     * @notice Set new implementation contract addresses
     * @param traderNftImpl_ New trader NFT implementation contract address
     * @param creatorNftImpl_ New creator NFT implementation contract address
     */
    function setImplementations(address traderNftImpl_, address creatorNftImpl_) external onlyOwner {
        address oldTrader = traderNftImpl;
        address oldCreator = creatorNftImpl;

        if (traderNftImpl_ != address(0)) {
            traderNftImpl = traderNftImpl_;
        }
        if (creatorNftImpl_ != address(0)) {
            creatorNftImpl = creatorNftImpl_;
        }

        emit ImplementationsUpdated(oldTrader, traderNftImpl, oldCreator, creatorNftImpl);
    }

    /**
     * @notice Set new owner address
     * @param owner_ New owner address
     */
    function setOwner(
        address owner_
    ) external onlyOwner {
        if (owner_ == address(0)) revert ZeroAddress();
        transferOwnership(owner_);
        admin = owner_;
    }

    /**
     * @notice Set new minimum USDT buy value
     * @param minimumUsdtBuyValue_ New minimum USDT buy value
     */
    function setMinimumUsdtBuyValue(
        uint256 minimumUsdtBuyValue_
    ) external onlyOwner {
        uint256 oldValue = minimumUsdtBuyValue;
        minimumUsdtBuyValue = minimumUsdtBuyValue_;
        emit MinimumBuyValueUpdated(oldValue, minimumUsdtBuyValue_);
    }

    /**
     * @notice Update distribution pool contract address
     * @param distributionPool_ New distribution pool contract address
     */
    function updateDistributionPool(
        address distributionPool_
    ) external onlyOwner {
        address oldPool = address(distributionPool);
        distributionPool = DistributionPool(payable(distributionPool_));
        emit DistributionPoolUpdated(oldPool, distributionPool_);
    }

    /**
     * @notice Authorize upgrade function
     * @dev Only contract owner can call
     * @param newImplementation New implementation contract address
     */
    // The following functions are overrides required by Solidity.
    function _authorizeUpgrade(
        address newImplementation
    ) internal view override onlyOwner {
        // Prevent upgrade to zero address
        if (newImplementation == address(0)) revert ZeroAddress();

        // Verify the new implementation supports UUPS
        // This prevents upgrading to a non-upgradeable contract which would lock the proxy
        bytes32 expectedSlot = 0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc; // ERC1967 implementation slot
        try IERC1822Proxiable(newImplementation).proxiableUUID() returns (bytes32 uuid) {
            if (uuid != expectedSlot) {
                revert("Invalid implementation UUID");
            }
        } catch {
            revert("Implementation does not support UUPS");
        }
    }
}
