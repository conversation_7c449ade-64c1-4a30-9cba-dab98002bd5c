// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

/**
 * @title PrecisionLib
 * @notice Library for handling precision conversions between stablecoins (6 or 18 decimals) and tokens (18 decimals)
 * @dev Provides unified functions for safe precision handling in bonding curve calculations
 */
library PrecisionLib {
    // ============ Constants ============

    /// @notice Standard token decimals (18)
    uint256 public constant TOKEN_DECIMALS = 18;

    /// @notice Token precision factor (10^18)
    uint256 public constant TOKEN_PRECISION = 10 ** TOKEN_DECIMALS;

    /// @notice WAD precision (10^18) - used by FixedPointMathLib
    uint256 public constant WAD = 10 ** 18;

    /// @notice Square root precision for bonding curve calculations (10^9)
    uint256 public constant SQRT_PRECISION = 10 ** 9;

    /// @notice Basis points precision (10,000 = 100%)
    uint256 public constant BASIS_POINTS = 10_000;

    // ============ Dynamic Precision Functions ============

    /**
     * @notice Get precision factor for a given number of decimals
     * @param decimals Number of decimal places
     * @return Precision factor (10^decimals)
     */
    function getPrecisionFactor(
        uint8 decimals
    ) internal pure returns (uint256) {
        return 10 ** decimals;
    }

    // ============ Precision Conversion Functions ============

    /**
     * @notice Convert stablecoin amount to 18 decimal precision
     * @param stablecoinAmount Amount in stablecoin precision
     * @param stablecoinDecimals Number of decimals the stablecoin uses
     * @return Amount scaled to 18 decimals
     */
    function scaleStablecoinToToken(
        uint256 stablecoinAmount,
        uint8 stablecoinDecimals
    ) internal pure returns (uint256) {
        if (stablecoinDecimals == TOKEN_DECIMALS) {
            return stablecoinAmount; // No scaling needed
        } else if (stablecoinDecimals < TOKEN_DECIMALS) {
            // Scale up (e.g., 6 decimals to 18 decimals)
            uint256 scaleFactor = getPrecisionFactor(uint8(TOKEN_DECIMALS - stablecoinDecimals));
            return stablecoinAmount * scaleFactor;
        } else {
            // Scale down (e.g., 24 decimals to 18 decimals) - rare case
            uint256 scaleFactor = getPrecisionFactor(uint8(stablecoinDecimals - TOKEN_DECIMALS));
            return stablecoinAmount / scaleFactor;
        }
    }

    /**
     * @notice Convert 18 decimal amount back to stablecoin precision
     * @param tokenAmount Amount in 18 decimal precision
     * @param stablecoinDecimals Number of decimals the stablecoin uses
     * @return Amount scaled to stablecoin precision
     */
    function scaleTokenToStablecoin(uint256 tokenAmount, uint8 stablecoinDecimals) internal pure returns (uint256) {
        if (stablecoinDecimals == TOKEN_DECIMALS) {
            return tokenAmount; // No scaling needed
        } else if (stablecoinDecimals < TOKEN_DECIMALS) {
            // Scale down (e.g., 18 decimals to 6 decimals)
            uint256 scaleFactor = getPrecisionFactor(uint8(TOKEN_DECIMALS - stablecoinDecimals));
            return tokenAmount / scaleFactor;
        } else {
            // Scale up (e.g., 18 decimals to 24 decimals) - rare case
            uint256 scaleFactor = getPrecisionFactor(uint8(stablecoinDecimals - TOKEN_DECIMALS));
            return tokenAmount * scaleFactor;
        }
    }

    // Legacy functions for backward compatibility
    /**
     * @notice Convert USDT amount to 18 decimal precision (legacy function)
     * @dev Assumes USDT has 18 decimals for backward compatibility
     * @param usdtAmount Amount in USDT precision
     * @return Amount in 18 decimals
     */
    function scaleUsdtToToken(
        uint256 usdtAmount
    ) internal pure returns (uint256) {
        return scaleStablecoinToToken(usdtAmount, 18);
    }

    /**
     * @notice Convert 18 decimal amount back to USDT precision (legacy function)
     * @dev Assumes USDT has 18 decimals for backward compatibility
     * @param tokenAmount Amount in 18 decimal precision
     * @return Amount in USDT precision
     */
    function scaleTokenToUsdt(
        uint256 tokenAmount
    ) internal pure returns (uint256) {
        return scaleTokenToStablecoin(tokenAmount, 18);
    }

    /**
     * @notice Convert square root result (9 decimals) to 18 decimal precision
     * @param sqrtAmount Amount in square root precision (9 decimals)
     * @return Amount scaled to 18 decimals
     */
    function scaleSqrtToToken(
        uint256 sqrtAmount
    ) internal pure returns (uint256) {
        return sqrtAmount * SQRT_PRECISION;
    }

    /**
     * @notice Convert 18 decimal amount to square root precision (9 decimals)
     * @param tokenAmount Amount in 18 decimal precision
     * @return Amount scaled to square root precision (9 decimals)
     */
    function scaleTokenToSqrt(
        uint256 tokenAmount
    ) internal pure returns (uint256) {
        return tokenAmount / SQRT_PRECISION;
    }

    // ============ Fee Calculation Functions ============

    /**
     * @notice Calculate fee using basis points for USDT amounts
     * @dev Precise calculation for USDT amounts
     * @param amount USDT amount (18 decimals)
     * @param feeRateBasisPoints Fee rate in basis points (e.g., 300 = 3%)
     * @return Fee amount in USDT precision (18 decimals)
     */
    function calculateFeeInBasisPoints(uint256 amount, uint256 feeRateBasisPoints) internal pure returns (uint256) {
        return (amount * feeRateBasisPoints) / BASIS_POINTS;
    }

    /**
     * @notice Convert WAD-based fee rate to basis points
     * @dev Converts from 18-decimal percentage to basis points
     * @param wadFeeRate Fee rate in WAD format (e.g., 3e16 = 3%)
     * @return Fee rate in basis points (e.g., 300 = 3%)
     */
    function wadToBasisPoints(
        uint256 wadFeeRate
    ) internal pure returns (uint256) {
        return (wadFeeRate * BASIS_POINTS) / WAD;
    }

    /**
     * @notice Calculate fee for stablecoin amounts using WAD rate with dynamic precision handling
     * @dev Ensures no precision loss for stablecoin amounts
     * @param stablecoinAmount Stablecoin amount in its native precision
     * @param wadFeeRate Fee rate in WAD format (e.g., 3e16 = 3%)
     * @param stablecoinDecimals Number of decimals the stablecoin uses (for documentation, not used in calculation)
     * @return Fee amount in stablecoin precision
     */
    function calculateStablecoinFeeFromWad(
        uint256 stablecoinAmount,
        uint256 wadFeeRate,
        uint8 stablecoinDecimals
    ) internal pure returns (uint256) {
        // Note: stablecoinDecimals is not used in calculation as fee is percentage-based
        stablecoinDecimals; // Silence compiler warning
        // Convert to basis points first to avoid precision loss
        uint256 basisPointsRate = wadToBasisPoints(wadFeeRate);
        return calculateFeeInBasisPoints(stablecoinAmount, basisPointsRate);
    }

    /**
     * @notice Calculate fee for USDT amounts using WAD rate but with proper precision handling (legacy function)
     * @dev Ensures no precision loss for USDT amounts, assumes 18 decimals for backward compatibility
     * @param usdtAmount USDT amount (18 decimals)
     * @param wadFeeRate Fee rate in WAD format (e.g., 3e16 = 3%)
     * @return Fee amount in USDT precision (18 decimals)
     */
    function calculateUsdtFeeFromWad(uint256 usdtAmount, uint256 wadFeeRate) internal pure returns (uint256) {
        return calculateStablecoinFeeFromWad(usdtAmount, wadFeeRate, 18);
    }

    // ============ Validation Functions ============

    /**
     * @notice Validate that an amount is properly scaled for USDT (18 decimals)
     * @param amount Amount to validate
     * @return True if amount appears to be in USDT precision
     */
    function isValidUsdtAmount(
        uint256 amount
    ) internal pure returns (bool) {
        // Check if amount is reasonable for USDT (basic sanity check)
        return amount >= SQRT_PRECISION || amount == 0;
    }

    /**
     * @notice Validate that an amount is properly scaled for tokens (18 decimals)
     * @param amount Amount to validate
     * @return True if amount appears to be in token precision
     */
    function isValidTokenAmount(
        uint256 amount
    ) internal pure returns (bool) {
        // Basic sanity check - amount should be divisible by some reasonable factor
        return amount >= SQRT_PRECISION || amount == 0;
    }

    // ============ Safe Math Functions ============

    /**
     * @notice Safely multiply two amounts and scale the result
     * @dev Prevents overflow in intermediate calculations
     * @param a First amount
     * @param b Second amount
     * @param scaleDown Scale factor to divide by
     * @return Result of (a * b) / scaleDown
     */
    function safeMulDiv(uint256 a, uint256 b, uint256 scaleDown) internal pure returns (uint256) {
        require(scaleDown > 0, "PrecisionLib: scale factor cannot be zero");

        // Check for overflow in multiplication
        if (a == 0 || b == 0) return 0;
        require(a <= type(uint256).max / b, "PrecisionLib: multiplication overflow");

        return (a * b) / scaleDown;
    }
}
