# 动态精度稳定币支持示例

这个示例展示了如何使用修改后的 DistributionPool 合约来支持不同精度的稳定币。

## 概述

修改后的合约现在可以同时处理：
- 6位精度的稳定币（如以太坊上的 USDC）
- 18位精度的稳定币（如某些链上的 USDT 或 DAI）

## 主要修改

### 1. PrecisionLib 库的增强

```solidity
// 新增的动态精度转换函数
function scaleStablecoinToToken(uint256 stablecoinAmount, uint8 stablecoinDecimals) 
    internal pure returns (uint256)

function scaleTokenToStablecoin(uint256 tokenAmount, uint8 stablecoinDecimals) 
    internal pure returns (uint256)

function calculateStablecoinFeeFromWad(
    uint256 stablecoinAmount, 
    uint256 wadFeeRate, 
    uint8 stablecoinDecimals
) internal pure returns (uint256)
```

### 2. DistributionPool 合约的修改

```solidity
// 使用 IERC20Metadata 而不是 IERC20
IERC20Metadata public usdtToken;

// 新增的精度获取函数
function getStablecoinDecimals() public view returns (uint8) {
    return usdtToken.decimals();
}

// 新增的精度转换辅助函数
function convertToStablecoinPrecision(uint256 amount) public view returns (uint256)
function convertFromStablecoinPrecision(uint256 amount) public view returns (uint256)
```

## 使用示例

### 示例 1：6位精度稳定币（USDC）

```solidity
// 假设使用 6 位精度的 USDC
// 用户想要用 100 USDC 购买代币

uint256 usdcAmount = 100 * 1e6; // 100 USDC (6 decimals)

// 合约会自动检测精度并进行转换
uint8 decimals = distributionPool.getStablecoinDecimals(); // 返回 6
uint256 tokenAmount = distributionPool.calculateTokenAmount(coinAddress, usdcAmount);

// 内部计算过程：
// 1. 将 100 * 1e6 转换为 100 * 1e18 进行计算
// 2. 计算应该获得的代币数量
// 3. 费用计算也会正确处理 6 位精度
```

### 示例 2：18位精度稳定币（DAI）

```solidity
// 假设使用 18 位精度的 DAI
// 用户想要用 100 DAI 购买代币

uint256 daiAmount = 100 * 1e18; // 100 DAI (18 decimals)

// 合约会自动检测精度
uint8 decimals = distributionPool.getStablecoinDecimals(); // 返回 18
uint256 tokenAmount = distributionPool.calculateTokenAmount(coinAddress, daiAmount);

// 内部计算过程：
// 1. 由于已经是 18 位精度，无需转换
// 2. 直接进行代币数量计算
// 3. 费用计算保持原有精度
```

## 关键特性

### 1. 向后兼容性
- 保留了所有原有的函数接口
- 旧的 `calculateUsdtFeeFromWad` 函数仍然可用
- 现有的合约调用不会受到影响

### 2. 自动精度检测
- 合约会自动从稳定币合约读取 `decimals()` 
- 所有计算都会根据实际精度进行调整

### 3. 精确的费用计算
- 费用计算考虑了不同精度的稳定币
- 避免了精度损失问题

## 测试验证

运行以下命令来验证功能：

```bash
forge test --match-contract DynamicPrecisionTest -vv
```

测试覆盖了：
- ✅ 6位精度到18位精度的转换
- ✅ 18位精度到6位精度的转换
- ✅ 往返转换的精度保持
- ✅ 费用计算的正确性
- ✅ 边界情况处理
- ✅ 向后兼容性

## 部署注意事项

1. **稳定币合约验证**：确保稳定币合约实现了 `IERC20Metadata` 接口
2. **精度测试**：在主网部署前，务必用实际的稳定币合约进行测试
3. **参数调整**：某些内部参数可能需要根据稳定币的实际精度进行调整

## 支持的稳定币示例

| 稳定币 | 网络 | 精度 | 地址示例 |
|--------|------|------|----------|
| USDC | Ethereum | 6 | 0xA0b86a33E6417c8f... |
| USDT | Ethereum | 6 | 0xdAC17F958D2ee523... |
| USDT | BSC | 18 | 0x55d398326f99059f... |
| DAI | Ethereum | 18 | 0x6B175474E89094C4... |

这个修改使得合约能够灵活地处理不同精度的稳定币，提高了合约的通用性和可用性。
